@echo off
setlocal enabledelayedexpansion

echo ========================================
echo Invoice Manager Network Configuration
echo ========================================
echo.

:: Check if PowerShell is available
powershell -Command "Get-Host" >nul 2>&1
if %errorlevel% neq 0 (
    echo PowerShell is not available. Using basic configuration...
    goto :basic_config
)

:: Use the Node.js setup script instead
echo Running Node.js configuration script...
cd frontend
node setup-env.js --ip=************
cd ..
goto :end

:basic_config
echo.
echo === Basic Network Configuration ===
echo.

:: Get IP address using basic method
for /f "tokens=2 delims=:" %%a in ('ipconfig ^| findstr /c:"IPv4 Address"') do (
    set "ip=%%a"
    set "ip=!ip: =!"
    if not "!ip!"=="" (
        set "detected_ip=!ip!"
        goto :found_ip
    )
)

:found_ip
if "%detected_ip%"=="" (
    echo Could not detect IP address automatically.
    set /p detected_ip="Please enter your IP address: "
)

echo Detected/Using IP: %detected_ip%
echo.

:: Configure frontend
echo Configuring frontend...
set "frontend_path=%~dp0..\frontend"
set "env_file=%frontend_path%\.env"

echo # Auto-generated environment file > "%env_file%"
echo # Backend API URL >> "%env_file%"
echo VITE_API_URL=http://%detected_ip%:8091/api >> "%env_file%"
echo. >> "%env_file%"
echo # Generated on: %date% %time% >> "%env_file%"
echo # IP Address: %detected_ip% >> "%env_file%"
echo # Mode: network >> "%env_file%"

echo ✓ Frontend configured
echo.

echo === Configuration Complete ===
echo.
echo To start your application:
echo.
echo 1. Start Backend:
echo    cd backend ^&^& mvn spring-boot:run
echo.
echo 2. Start Frontend:
echo    cd frontend ^&^& npm run dev
echo.
echo Access URLs:
echo    Local: http://localhost:3060
echo    Network: http://%detected_ip%:3060
echo.
echo From other devices: http://%detected_ip%:3060
echo.
echo IMPORTANT: Make sure Windows Firewall allows:
echo    - Port 3060 (Frontend)
echo    - Port 8091 (Backend)

:end
echo.
pause
