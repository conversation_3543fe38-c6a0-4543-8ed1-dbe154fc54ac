// Network API Test Script
// This script tests all API endpoints from the network IP

const SERVER_IP = '************';
const SERVER_PORT = '8091';
const BASE_URL = `http://${SERVER_IP}:${SERVER_PORT}`;

// Test endpoints
const endpoints = {
  'SPOCs': [
    `${BASE_URL}/api/spocs`,
    `${BASE_URL}/api/spocs/getAll`
  ],
  'Projects': [
    `${BASE_URL}/projects/getAll`,
    `${BASE_URL}/api/projects`,
    `${BASE_URL}/api/noauth/getProjects`
  ],
  'Invoices': [
    `${BASE_URL}/invoices`,
    `${BASE_URL}/api/invoices`
  ],
  'Payments': [
    `${BASE_URL}/api/payments/getAll`
  ],
  'Redberyl Accounts': [
    `${BASE_URL}/redberyl-accounts/getAll`,
    `${BASE_URL}/api/redberyl-accounts`
  ],
  'Clients': [
    `${BASE_URL}/clients`,
    `${BASE_URL}/api/clients`
  ],
  'Candidates': [
    `${BASE_URL}/candidates`,
    `${BASE_URL}/api/candidates`
  ],
  'BDMs': [
    `${BASE_URL}/bdms`,
    `${BASE_URL}/v1/bdms`
  ],
  'HSN Codes': [
    `${BASE_URL}/api/hsn-codes`
  ],
  'Invoice Types': [
    `${BASE_URL}/api/noauth/invoice-types`,
    `${BASE_URL}/noauth/invoice-types`
  ],
  'Documents': [
    `${BASE_URL}/api/document-templates`,
    `${BASE_URL}/api/generated-documents`
  ]
};

// Test function
async function testEndpoint(name, url) {
  try {
    console.log(`Testing ${name}: ${url}`);
    
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json'
      }
    });
    
    if (response.ok) {
      const data = await response.json();
      console.log(`✅ ${name} - SUCCESS: ${Array.isArray(data) ? data.length : 'N/A'} items`);
      return true;
    } else {
      console.log(`❌ ${name} - FAILED: ${response.status} ${response.statusText}`);
      return false;
    }
  } catch (error) {
    console.log(`❌ ${name} - ERROR: ${error.message}`);
    return false;
  }
}

// Test all endpoints
async function testAllEndpoints() {
  console.log('='.repeat(60));
  console.log('TESTING ALL API ENDPOINTS FROM NETWORK IP');
  console.log('='.repeat(60));
  console.log(`Server: ${BASE_URL}`);
  console.log('');
  
  const results = {};
  
  for (const [category, urls] of Object.entries(endpoints)) {
    console.log(`\n--- Testing ${category} ---`);
    results[category] = [];
    
    for (const url of urls) {
      const success = await testEndpoint(category, url);
      results[category].push({ url, success });
    }
  }
  
  // Summary
  console.log('\n' + '='.repeat(60));
  console.log('SUMMARY');
  console.log('='.repeat(60));
  
  for (const [category, tests] of Object.entries(results)) {
    const successCount = tests.filter(t => t.success).length;
    const totalCount = tests.length;
    const status = successCount > 0 ? '✅' : '❌';
    console.log(`${status} ${category}: ${successCount}/${totalCount} endpoints working`);
  }
  
  console.log('\nIf any endpoints are failing, check:');
  console.log('1. Backend is running on port 8091');
  console.log('2. CORS is configured properly');
  console.log('3. Firewall allows port 8091');
  console.log('4. Network connectivity to ************');
}

// Run the test
if (typeof window !== 'undefined') {
  // Browser environment
  testAllEndpoints();
} else {
  // Node.js environment
  console.log('Run this script in the browser console on the other PC');
  console.log('Copy and paste this entire script into the browser console at:');
  console.log('http://************:3060');
}
