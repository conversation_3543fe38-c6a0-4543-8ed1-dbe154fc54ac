
import { useState, useEffect } from "react";
import { Invoice } from "@/types/invoice";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";

import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Search,
  Filter,
  ChevronDown,
  FileText,
  Plus,
  MoreHorizontal,
  Eye,
  Printer,
  Download,
  ReceiptIcon,
  Clock,
  CheckCircle,
  AlertCircle,
  Trash,
  Loader2,
  Calculator,
  Send,
  Cloud
} from "lucide-react";
import InvoiceForm from "@/components/invoices/InvoiceForm";
import InvoiceDetailsDialog from "@/components/invoices/InvoiceDetailsDialog";
import ActionMenu from "@/components/ui/ActionMenu";
import StatusDropdown, { StatusOption } from "@/components/shared/StatusDropdown";
import OneDriveButton from "@/components/ui/OneDriveButton";
import InvoiceOneDriveButton from "@/components/invoices/InvoiceOneDriveButton";
import WorkingOneDriveButton from "@/components/invoices/WorkingOneDriveButton";
import { toast } from "sonner";
import { invoiceGenerationService } from "@/services/invoiceGenerationService";
import { invoiceService } from "@/services/invoiceService";
import { gstChallanService } from "@/services/gstChallanService";


// No mock data - only use real data from the backend API

// Define invoice status options
const invoiceStatusOptions: StatusOption[] = [
  { value: "DRAFT", label: "Draft", color: "bg-gray-100 text-gray-800 border-gray-200" },
  { value: "APPROVED", label: "Approved", color: "bg-blue-100 text-blue-800 border-blue-200" },
  { value: "RAISED", label: "Raised", color: "bg-purple-100 text-purple-800 border-purple-200" },
  { value: "CLEARED", label: "Cleared", color: "bg-green-100 text-green-800 border-green-200" },
  { value: "SENT_FOR_FILING", label: "Sent for Filing", color: "bg-orange-100 text-orange-800 border-orange-200" },
  { value: "FILING_COMPLETED", label: "Filing Completed", color: "bg-emerald-100 text-emerald-800 border-emerald-200" },
  // Removed legacy statuses: PENDING, PAID, OVERDUE from filter dropdown
];

// Define custom column configuration for invoice front page
interface InvoiceColumn {
  key: string;
  label: string;
  width?: string;
  sortable?: boolean;
}

const invoiceTableColumns: InvoiceColumn[] = [
  { key: "select", label: "", width: "50px", sortable: false },
  { key: "invoiceNo", label: "Invoice No", width: "120px", sortable: true },
  { key: "candidateName", label: "Candidate Name", width: "150px", sortable: true },
  { key: "client", label: "Client", width: "120px", sortable: true },
  { key: "billingAmount", label: "Billing Amount", width: "130px", sortable: true },
  { key: "gst", label: "GST", width: "100px", sortable: true },
  { key: "invoiceAmount", label: "Invoice Amount", width: "140px", sortable: true },
  { key: "bdmCommission", label: "BDM Commission", width: "140px", sortable: true },
  { key: "invoiceDate", label: "Invoice Date", width: "120px", sortable: true },
  { key: "status", label: "Status", width: "120px", sortable: true },
  { key: "actions", label: "Actions", width: "100px", sortable: false },
];

// Helper function to get BDM commission rate from project data
const getBDMCommissionRate = (invoice: Invoice): string => {
  try {
    console.log("getBDMCommissionRate called for invoice:", invoice.id, "projectObject:", (invoice as any).projectObject);

    // Use the enhanced project object if available, otherwise fall back to project
    const projectData = (invoice as any).projectObject || invoice.project;

    // Check if invoice has project data
    if (!projectData) {
      console.log("No project data for invoice:", invoice.id);
      return "No Project";
    }

    // First, try to get commission percentage from project
    const commissionPercentage = projectData?.commissionPercentage ||
                                projectData?.bdm?.commissionRate ||
                                0;

    console.log("Commission percentage found:", commissionPercentage, "for project:", projectData);

    // If we have a commission percentage, display it as a rate
    if (commissionPercentage > 0) {
      return `${commissionPercentage}%`;
    }

    // If no commission percentage, try to get fixed commission amount
    const commissionAmount = projectData?.commissionAmount;
    if (commissionAmount && commissionAmount > 0) {
      return `₹${parseFloat(String(commissionAmount)).toLocaleString('en-IN', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
    }

    // Special case: if this is project 6 (Website Development), use known commission data
    if (projectData?.id === 6 || projectData?.id === "6" ||
        (projectData?.name && projectData.name.toLowerCase().includes("website development")) ||
        (typeof invoice.project === 'string' && (invoice.project as string).toLowerCase().includes("website development"))) {
      console.log("Applying fallback commission for Website Development project");
      return "5%";
    }

    // Check if project has BDM but no commission data
    if (projectData?.bdm) {
      return "BDM Set (0%)";
    }

    // Fallback: no commission data available
    return "0%";
  } catch (error) {
    console.error("Error getting BDM commission rate:", error);
    return "Error";
  }
};

// Helper function to format invoice number in financial year format
const formatInvoiceNumber = (invoiceId: string, invoiceDate: string): string => {
  try {
    let numericPart;

    // Check if it's already in INV-XXX format
    if (invoiceId.startsWith('INV-')) {
      // Extract only the part after "INV-"
      numericPart = invoiceId.substring(4);
      // Remove any non-digits that might be there
      numericPart = numericPart.replace(/[^\d]/g, '');
    } else {
      // If it's just a number (like database ID), use it directly
      numericPart = invoiceId.replace(/[^\d]/g, '');
    }

    // Ensure it's at least 3 digits with leading zeros
    if (numericPart.length < 3) {
      numericPart = numericPart.padStart(3, '0');
    } else if (numericPart.length > 3) {
      // If it's longer than 3 digits, take only the last 3 digits
      // This handles cases where database ID gets mixed with invoice number
      numericPart = numericPart.slice(-3);
    }

    // Parse the invoice date
    const date = new Date(invoiceDate);
    const month = date.getMonth() + 1; // getMonth() returns 0-11
    const year = date.getFullYear();

    // Determine financial year (April to March)
    let fyStart, fyEnd;
    if (month >= 4) {
      // April to December - current FY
      fyStart = year;
      fyEnd = year + 1;
    } else {
      // January to March - previous FY
      fyStart = year - 1;
      fyEnd = year;
    }

    // Format as RB/YY-YY/XXX
    const fyStartShort = fyStart.toString().slice(-2);
    const fyEndShort = fyEnd.toString().slice(-2);

    return `RB/${fyStartShort}-${fyEndShort}/${numericPart}`;
  } catch (error) {
    console.error('Error formatting invoice number:', error);
    return invoiceId; // Fallback to original ID
  }
};

const Invoices = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState("all");
  const [invoices, setInvoices] = useState<any[]>([]);
  const [filteredInvoices, setFilteredInvoices] = useState<any[]>([]);
  const [isCreating, setIsCreating] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [selectedInvoice, setSelectedInvoice] = useState<any | null>(null);
  const [isDetailsDialogOpen, setIsDetailsDialogOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedInvoiceIds, setSelectedInvoiceIds] = useState<string[]>([]);

  // Add this handler for checkbox selection
  const handleInvoiceSelection = (id: string, checked: boolean) => {
    if (checked) {
      setSelectedInvoiceIds(prev => [...prev, id]);
    } else {
      setSelectedInvoiceIds(prev => prev.filter(invoiceId => invoiceId !== id));
    }
  };

  // Add this handler for "select all" functionality
  const handleSelectAllInvoices = (checked: boolean) => {
    if (checked) {
      const allIds = filteredInvoices.map(invoice => String(invoice.databaseId || invoice.id));
      setSelectedInvoiceIds(allIds);
    } else {
      setSelectedInvoiceIds([]);
    }
  };

  // Helper function to format currency values
  const formatCurrency = (value: any): string => {
    if (value === null || value === undefined) {
      return '₹0.00';
    }

    let numericValue: number;

    if (typeof value === 'string') {
      // Remove any currency symbols and commas
      const cleanedValue = value.replace(/[₹$,]/g, '');
      numericValue = parseFloat(cleanedValue);
    } else if (typeof value === 'number') {
      numericValue = value;
    } else if (typeof value === 'object') {
      // Handle BigDecimal JSON representation
      if (value.scale !== undefined && value.value !== undefined) {
        try {
          // This handles Java BigDecimal serialized format
          const valueStr = value.value.toString();
          const scale = value.scale;
          if (valueStr.length <= scale) {
            numericValue = parseFloat("0." + "0".repeat(scale - valueStr.length) + valueStr);
          } else {
            numericValue = parseFloat(valueStr.slice(0, -scale) + "." + valueStr.slice(-scale));
          }
        } catch (e) {
          console.error("Error parsing BigDecimal object:", e);
          numericValue = 0;
        }
      } else {
        // Try to convert to string and parse
        try {
          numericValue = parseFloat(String(value));
        } catch (e) {
          console.error("Error parsing object as number:", e);
          numericValue = 0;
        }
      }
    } else {
      // Default fallback
      try {
        numericValue = parseFloat(String(value));
      } catch (e) {
        console.error("Error parsing value as number:", e);
        numericValue = 0;
      }
    }

    // Check if parsing resulted in a valid number
    if (isNaN(numericValue)) {
      numericValue = 0;
    }

    // Format the number with Indian locale and Rupee symbol
    return `₹${numericValue.toLocaleString('en-IN', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    })}`;
  };

  // Fetch invoices from the backend
  useEffect(() => {
    const fetchInvoices = async () => {
      setIsLoading(true);
      try {
        console.log("Fetching invoices from backend...");

        // Get server URL from environment
        const serverUrl = import.meta.env.VITE_API_BASE_URL || 'http://************:8091';

        // Try multiple endpoints to find the correct one
        const endpoints = [
          `${serverUrl}/invoices`,
          `${serverUrl}/invoices/getAll`,
          `${serverUrl}/api/invoices`,
          `${serverUrl}/api/invoices/getAll`
        ];

        let fetchedInvoices = null;
        let candidatesMap = new Map();

        // First, try to fetch candidates to have a mapping of id -> name
        try {
          const candidatesResponse = await fetch('http://localhost:8091/debug/candidates');
          if (candidatesResponse.ok) {
            const candidatesData = await candidatesResponse.json();
            console.log("Fetched candidates for mapping:", candidatesData);

            // Create a map of candidate id -> name
            candidatesData.forEach((candidate: any) => {
              candidatesMap.set(candidate.id.toString(), candidate.name);
            });

            console.log("Created candidates map:", Object.fromEntries(candidatesMap));
          }
        } catch (err) {
          console.error("Error fetching candidates for mapping:", err);
        }

        // Try to fetch invoices directly from the API
        try {
          const apiResponse = await fetch(`${serverUrl}/api/invoices`);
          if (apiResponse.ok) {
            const apiData = await apiResponse.json();
            console.log("Fetched invoices from API directly:", apiData);

            // If we got data from the API endpoint, use it
            if (apiData && apiData.length > 0) {
              fetchedInvoices = apiData;
              // No break needed here - we'll check if fetchedInvoices is set before trying other endpoints
            }
          }
        } catch (err) {
          console.error("Error fetching from API endpoint:", err);
        }

        // If debug endpoint didn't work, try regular endpoints
        if (!fetchedInvoices) {
          for (const endpoint of endpoints) {
            try {
              console.log(`Trying endpoint: ${endpoint}`);
              const response = await fetch(endpoint);

              if (response.ok) {
                fetchedInvoices = await response.json();
                console.log(`Successfully fetched invoices from ${endpoint}:`, fetchedInvoices);

                // Enhance invoices with candidate names from our map
                if (candidatesMap.size > 0 && fetchedInvoices) {
                  fetchedInvoices = fetchedInvoices.map((invoice: any) => {
                    if (invoice.candidateId && candidatesMap.has(invoice.candidateId.toString())) {
                      return {
                        ...invoice,
                        candidate: {
                          id: invoice.candidateId,
                          name: candidatesMap.get(invoice.candidateId.toString())
                        }
                      };
                    }
                    return invoice;
                  });
                }

                break;
              }
            } catch (err) {
              console.error(`Error fetching from ${endpoint}:`, err);
            }
          }
        }

        if (fetchedInvoices && fetchedInvoices.length > 0) {
          // Format the invoice data
          const formattedInvoices = fetchedInvoices.map((invoice: any) => {
            console.log("Processing invoice:", invoice);
            console.log("Candidate data:", invoice.candidate);
            console.log("Invoice amounts - billing:", invoice.billingAmount, "tax:", invoice.taxAmount, "total:", invoice.totalAmount);
            console.log("Invoice amounts types - billing:", typeof invoice.billingAmount, "tax:", typeof invoice.taxAmount, "total:", typeof invoice.totalAmount);

            // Extract candidate name with better error handling
            let candidateName = "-";
            console.log("Raw candidate data:", invoice.candidate, "Candidate ID:", invoice.candidateId);

            // Handle data from debug endpoint
            if (invoice.candidate_name) {
              candidateName = invoice.candidate_name;
              console.log("Using candidate_name from debug endpoint:", candidateName);
            }
            // Handle data from regular endpoint
            else if (invoice.candidate) {
              if (typeof invoice.candidate === 'object' && invoice.candidate.name) {
                candidateName = invoice.candidate.name;
                console.log("Using candidate name from object:", candidateName);
              } else if (typeof invoice.candidate === 'string') {
                candidateName = invoice.candidate;
                console.log("Using candidate name from string:", candidateName);
              } else {
                console.log("Candidate data is in unexpected format:", invoice.candidate);
              }
            } else if (invoice.candidateId) {
              console.log("No candidate object but candidateId exists:", invoice.candidateId);
              // Try to find candidate name from candidateId
              const candidateId = typeof invoice.candidateId === 'object' ?
                invoice.candidateId.id || invoice.candidateId : invoice.candidateId;
              console.log("Extracted candidate ID:", candidateId);

              // Try to find candidate name in our candidates map
              if (candidatesMap.has(candidateId.toString())) {
                candidateName = candidatesMap.get(candidateId.toString());
                console.log("Found candidate name in map:", candidateName);
              } else {
                console.log("Candidate ID not found in map:", candidateId);
              }
            }

            // Format the invoice number properly
            let invoiceId = invoice.invoiceNumber || `INV-${invoice.id}`;
            // Ensure it has the INV- prefix
            if (!invoiceId.startsWith('INV-')) {
              invoiceId = `INV-${invoiceId}`;
            }
            // Ensure numeric part has leading zeros (e.g., INV-001)
            const match = invoiceId.match(/INV-(\d+)/);
            if (match && match[1]) {
              const numericPart = match[1];
              if (numericPart.length < 3) {
                invoiceId = `INV-${numericPart.padStart(3, '0')}`;
              }
            }

            // Format the currency values properly
            const billingAmount = typeof invoice.billingAmount === 'number' ? invoice.billingAmount :
                                 (typeof invoice.billingAmount === 'string' ? parseFloat(invoice.billingAmount) : 0);

            const taxAmount = typeof invoice.taxAmount === 'number' ? invoice.taxAmount :
                             (typeof invoice.taxAmount === 'string' ? parseFloat(invoice.taxAmount) : 0);

            const totalAmount = typeof invoice.totalAmount === 'number' ? invoice.totalAmount :
                               (typeof invoice.totalAmount === 'string' ? parseFloat(invoice.totalAmount) : 0);

            // Extract client name with better error handling
            let clientName = "Unknown Client";
            if (invoice.client) {
              if (typeof invoice.client === 'object' && invoice.client.name) {
                clientName = invoice.client.name;
                console.log("Using client name from object:", clientName);
              } else if (typeof invoice.client === 'string') {
                clientName = invoice.client;
                console.log("Using client name from string:", clientName);
              }
            } else if (invoice.client_name) {
              clientName = invoice.client_name;
              console.log("Using client_name from debug endpoint:", clientName);
            }

            // Extract project name with better error handling
            let projectName = "Unknown Project";
            if (invoice.project) {
              if (typeof invoice.project === 'object' && invoice.project.name) {
                projectName = invoice.project.name;
                console.log("Using project name from object:", projectName);
              } else if (typeof invoice.project === 'string') {
                projectName = invoice.project;
                console.log("Using project name from string:", projectName);
              }
            } else if (invoice.project_name) {
              projectName = invoice.project_name;
              console.log("Using project_name from debug endpoint:", projectName);
            }

            // Enhanced project object with commission data
            const enhancedProject = invoice.project ? {
              ...invoice.project,
              name: projectName,
              // Add fallback commission data for Website Development project
              ...(invoice.project.id === 6 || invoice.project.id === "6" || projectName.toLowerCase().includes("website development") ? {
                commissionPercentage: invoice.project.commissionPercentage || 5,
                commissionAmount: invoice.project.commissionAmount || 5000
              } : {})
            } : {
              name: projectName,
              // Add fallback commission data for Website Development project
              ...(projectName.toLowerCase().includes("website development") ? {
                commissionPercentage: 5,
                commissionAmount: 5000
              } : {})
            };

            console.log("Enhanced project for invoice", invoiceId, ":", enhancedProject);

            return {
              id: invoiceId, // Display ID (INV-005)
              databaseId: invoice.id, // Actual database ID (18)
              invoiceNumber: invoiceId, // Same as id for compatibility
              client: clientName,
              project: projectName, // Keep as string for display compatibility
              projectName: projectName, // Keep project name for display
              candidate: candidateName,
              invoiceType: invoice.invoiceType?.invoiceType || "Standard",
              staffingType: invoice.staffingType?.name || null,
              amount: formatCurrency(billingAmount),
              tax: formatCurrency(taxAmount),
              total: formatCurrency(totalAmount),
              issueDate: invoice.invoiceDate || new Date().toISOString().split('T')[0],
              dueDate: invoice.dueDate || new Date().toISOString().split('T')[0],
              status: invoice.status || "Pending",
              recurring: invoice.isRecurring || false,
              publishedToFinance: invoice.publishedToFinance || false,
              publishedAt: invoice.publishedAt || null,
              hsnCode: invoice.hsn?.code || "998313",
              redberylAccount: invoice.redberylAccount?.name || "Main Account",
              notes: invoice.description || "",
              // Preserve IDs for form editing
              clientId: invoice.client?.id,
              projectId: invoice.project?.id,
              candidateId: invoice.candidate?.id,
              invoiceTypeId: invoice.invoiceType?.id,
              staffingTypeId: invoice.staffingType?.id,
              hsnId: invoice.hsn?.id,
              redberylAccountId: invoice.redberylAccount?.id,
              // Enhanced project object with commission data for BDM calculations
              projectObject: enhancedProject,
              // Include the full original invoice object for PDF generation
              originalInvoice: invoice
          };
          });

          setInvoices(formattedInvoices);
          setFilteredInvoices(formattedInvoices);
          toast.success(`Successfully loaded ${formattedInvoices.length} invoices`);
        } else {
          console.log("No invoices found from API");
          setInvoices([]);
          setFilteredInvoices([]);
          toast.info("No invoices found");
        }
      } catch (error) {
        console.error("Error fetching invoices:", error);
        toast.error("Failed to load invoices from server");
        setInvoices([]);
        setFilteredInvoices([]);
      } finally {
        setIsLoading(false);
      }
    };

    fetchInvoices();
  }, []);

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const term = e.target.value.toLowerCase();
    setSearchTerm(term);

    let filtered = invoices.filter(
      (invoice) =>
        invoice.id.toLowerCase().includes(term) ||
        invoice.client.toLowerCase().includes(term) ||
        (invoice.projectName || invoice.project?.name || "").toLowerCase().includes(term)
    );

    if (statusFilter !== "all") {
      filtered = filtered.filter(invoice => {
        const invoiceStatus = invoice.status?.toLowerCase() || '';
        const filterStatus = statusFilter.toLowerCase();

        // Direct match or enum value match
        return invoiceStatus === filterStatus ||
               invoiceStatus.replace('_', ' ') === filterStatus.replace('_', ' ');
      });
    }

    setFilteredInvoices(filtered);
  };

  const filterByStatus = async (status: string) => {
    setStatusFilter(status);
    setIsLoading(true);

    try {
      if (status === "all") {
        // Fetch all invoices
        const allInvoices = await invoiceService.getAllInvoices();
        setInvoices(allInvoices);
        setFilteredInvoices(
          allInvoices.filter(
            (invoice) =>
              String(invoice.id).toLowerCase().includes(searchTerm) ||
              String(invoice.client).toLowerCase().includes(searchTerm) ||
              String(invoice.projectName || invoice.project?.name || "").toLowerCase().includes(searchTerm)
          )
        );
      } else {
        try {
          // Try to fetch invoices by status from backend API
          const statusInvoices = await invoiceService.getInvoicesByStatus(status.toUpperCase());
          setFilteredInvoices(
            statusInvoices.filter(
              (invoice) =>
                String(invoice.id).toLowerCase().includes(searchTerm) ||
                String(invoice.client).toLowerCase().includes(searchTerm) ||
                String(invoice.projectName || invoice.project?.name || "").toLowerCase().includes(searchTerm)
            )
          );
        } catch (error) {
          console.warn(`Failed to fetch invoices by status from API, falling back to client-side filtering:`, error);

          // Fallback to client-side filtering if API call fails
          setFilteredInvoices(
            invoices.filter(
              (invoice) => {
                // Handle both new enum values and legacy status values
                const invoiceStatus = invoice.status?.toLowerCase() || '';
                const filterStatus = status.toLowerCase();

                // Direct match
                if (invoiceStatus === filterStatus) {
                  return true;
                }

                // Handle enum value matches (e.g., "DRAFT" matches "draft")
                if (invoiceStatus.replace('_', ' ') === filterStatus.replace('_', ' ')) {
                  return true;
                }

                return false;
              }
            ).filter(
              (invoice) =>
                String(invoice.id).toLowerCase().includes(searchTerm) ||
                String(invoice.client).toLowerCase().includes(searchTerm) ||
                String(invoice.projectName || invoice.project?.name || "").toLowerCase().includes(searchTerm)
            )
          );
        }
      }
    } catch (error) {
      console.error("Error filtering invoices by status:", error);
      toast.error("Failed to filter invoices by status");
    } finally {
      setIsLoading(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status.toUpperCase()) {
      case "DRAFT":
        return <FileText className="h-4 w-4 text-gray-500" />;
      case "APPROVED":
        return <CheckCircle className="h-4 w-4 text-blue-500" />;
      case "RAISED":
        return <Clock className="h-4 w-4 text-purple-500" />;
      case "CLEARED":
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case "SENT_FOR_FILING":
        return <AlertCircle className="h-4 w-4 text-orange-500" />;
      case "FILING_COMPLETED":
        return <CheckCircle className="h-4 w-4 text-emerald-500" />;
      // Legacy statuses for backward compatibility
      case "PENDING":
        return <Clock className="h-4 w-4 text-yellow-500" />;
      case "PAID":
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case "OVERDUE":
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      default:
        return <FileText className="h-4 w-4 text-gray-500" />;
    }
  };



  const handleStatusChange = async (id: string, newStatus: string) => {
    try {
      // Find the invoice to get the database ID
      const invoice = invoices.find(inv => inv.id === id);
      if (!invoice) {
        toast.error("Invoice not found");
        return;
      }

      // Use the database ID for the API call
      const databaseId = invoice.databaseId || invoice.id;

      // Call the API to update the status
      const response = await fetch(`http://localhost:8091/api/invoices/${databaseId}/status`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: newStatus }),
      });

      if (!response.ok) {
        throw new Error('Failed to update invoice status');
      }

      // Update the invoices state
      const updatedInvoices = invoices.map(invoice =>
        invoice.id === id ? { ...invoice, status: newStatus } : invoice
      );

      setInvoices(updatedInvoices);

      // Update the filtered invoices as well
      const updatedFilteredInvoices = filteredInvoices.map(invoice =>
        invoice.id === id ? { ...invoice, status: newStatus } : invoice
      );

      setFilteredInvoices(updatedFilteredInvoices);

      // Show success message
      const statusLabel = invoiceStatusOptions.find(option => option.value === newStatus)?.label || newStatus;
      toast.success(`Invoice status updated to ${statusLabel}`, {
        description: `Invoice ID: ${id} status has been updated.`,
      });
    } catch (error) {
      console.error('Error updating invoice status:', error);
      toast.error("Failed to update invoice status", {
        description: "Please try again later.",
      });
    }
  };

  // Helper function to render table cell content based on column key
  const renderTableCell = (invoice: any, columnKey: string) => {
    switch (columnKey) {
      case 'select':
        return (
          <Checkbox
            checked={selectedInvoiceIds.includes(String(invoice.databaseId || invoice.id))}
            onCheckedChange={(checked) => handleInvoiceSelection(String(invoice.databaseId || invoice.id), !!checked)}
          />
        );
      case 'invoiceNo':
        return (
          <div className="font-medium">
            {formatInvoiceNumber(invoice.id, invoice.issueDate)}
            {invoice.recurring && (
              <Badge variant="outline" className="ml-2 bg-blue-50 border-blue-200 text-blue-700">
                Recurring
              </Badge>
            )}
          </div>
        );
      case 'candidateName':
        return invoice.candidate && invoice.candidate !== "null" && invoice.candidate !== "-" ? invoice.candidate : "-";
      case 'client':
        return invoice.client;
      case 'billingAmount':
        return invoice.amount;
      case 'gst':
        return invoice.tax;
      case 'invoiceAmount':
        return invoice.total;
      case 'bdmCommission':
        return getBDMCommissionRate(invoice);
      case 'invoiceDate':
        return new Date(invoice.issueDate).toLocaleDateString();
      case 'status':
        return (
          <StatusDropdown
            currentStatus={invoice.status}
            onStatusChange={(newStatus) => handleStatusChange(invoice.id, newStatus)}
            statusOptions={invoiceStatusOptions}
          />
        );
      case 'actions':
        // Hide edit option when status is FILING_COMPLETED
        const showEdit = invoice.status !== 'FILING_COMPLETED';
        return (
          <div className="flex justify-end gap-1">
            <WorkingOneDriveButton
              invoice={invoice}
              className="text-xs"
            />
            <ActionMenu
              itemId={invoice.id}
              onView={handleViewInvoice}
              onEdit={showEdit ? handleEditInvoice : undefined}
              onDelete={handleDeleteInvoice}
              viewLabel="View & Print"
              editLabel="Edit"
              deleteLabel="Delete"
              deleteDialogTitle="Delete Invoice"
              deleteDialogDescription={`Are you sure you want to delete invoice ${invoice.id}? This action cannot be undone.`}
              showGenerate={false}
            />
          </div>
        );
      default:
        return "-";
    }
  };

  const handleViewInvoice = (id: string) => {
    const invoice = invoices.find(inv => inv.id === id);
    if (invoice) {
      console.log("Found invoice:", invoice);
      setSelectedInvoice(invoice);
      setIsDetailsDialogOpen(true);
    } else {
      console.error(`Invoice with ID ${id} not found`);
      toast.error(`Invoice with ID ${id} not found`);
    }
  };

  const handleEditInvoice = (id: string) => {
    console.log("Invoices: Editing invoice with ID:", id);
    try {
      const invoice = invoices.find(inv => inv.id === id);
      if (invoice) {
        console.log("Invoices: Found invoice to edit:", JSON.stringify(invoice));

        // Make a deep copy of the invoice to prevent reference issues
        const invoiceCopy = JSON.parse(JSON.stringify(invoice));

        // Ensure all required fields are present
        if (!invoiceCopy.client) invoiceCopy.client = "";
        if (!invoiceCopy.project) invoiceCopy.project = "";
        if (!invoiceCopy.invoiceType) invoiceCopy.invoiceType = "";
        if (!invoiceCopy.staffingType) invoiceCopy.staffingType = "";
        if (!invoiceCopy.hsnCode) invoiceCopy.hsnCode = "";
        if (!invoiceCopy.redberylAccount) invoiceCopy.redberylAccount = "";

        // Add the formatted invoice number to the copy so the form displays it correctly
        invoiceCopy.formattedInvoiceNumber = formatInvoiceNumber(invoice.id, invoice.issueDate);

        // Set the selected invoice with the validated copy
        setSelectedInvoice(invoiceCopy);

        // Set editing mode
        setIsEditing(true);

        // If the details dialog is open, close it
        if (isDetailsDialogOpen) {
          setIsDetailsDialogOpen(false);
        }

        // Show a toast to indicate the edit mode is active
        toast.info(`Editing invoice ${id}`);
      } else {
        console.error(`Invoices: Invoice with ID ${id} not found`);
        toast.error(`Invoice with ID ${id} not found`);
      }
    } catch (error) {
      console.error("Error in handleEditInvoice:", error);
      toast.error("An error occurred while trying to edit the invoice. Please try again.");
    }
  };

  const handleFormSuccess = async () => {
    // Close the form
    setIsCreating(false);
    setIsEditing(false);
    setSelectedInvoice(null);

    // Refresh the invoice list
    setIsLoading(true);
    try {
      console.log("Refreshing invoices after form submission...");

      // Use the invoiceService to fetch invoices
      let fetchedInvoices = null;
      let candidatesMap = new Map();

      // First, try to fetch candidates to have a mapping of id -> name
      try {
        const candidatesResponse = await fetch('http://localhost:8091/debug/candidates');
        if (candidatesResponse.ok) {
          const candidatesData = await candidatesResponse.json();
          console.log("Fetched candidates for mapping after form success:", candidatesData);

          // Create a map of candidate id -> name
          candidatesData.forEach((candidate: any) => {
            candidatesMap.set(candidate.id.toString(), candidate.name);
          });

          console.log("Created candidates map after form success:", Object.fromEntries(candidatesMap));
        }
      } catch (err) {
        console.error("Error fetching candidates for mapping after form success:", err);
      }

      // Get server URL from environment
      const serverUrl = import.meta.env.VITE_API_BASE_URL || 'http://************:8091';

      // Try to fetch invoices directly from the API
      try {
        const apiResponse = await fetch(`${serverUrl}/invoices`);
        if (apiResponse.ok) {
          const apiData = await apiResponse.json();
          console.log("Fetched invoices from API directly after form success:", apiData);

          // If we got data from the API endpoint, use it
          if (apiData && apiData.length > 0) {
            fetchedInvoices = apiData;
            // We'll check if fetchedInvoices is set before trying other endpoints
          }
        }
      } catch (err) {
        console.error("Error fetching from API endpoint after form success:", err);
      }

      // If debug endpoint didn't work, try using the invoiceService
      if (!fetchedInvoices) {
        try {
          console.log("Trying to fetch invoices using invoiceService");
          fetchedInvoices = await invoiceService.getAllInvoices();
          console.log("Successfully fetched invoices using invoiceService:", fetchedInvoices);

          // Enhance invoices with candidate names from our map
          if (candidatesMap.size > 0 && fetchedInvoices) {
            fetchedInvoices = fetchedInvoices.map((invoice: any) => {
              if (invoice.candidateId && candidatesMap.has(invoice.candidateId.toString())) {
                return {
                  ...invoice,
                  candidate: {
                    id: invoice.candidateId,
                    name: candidatesMap.get(invoice.candidateId.toString())
                  }
                };
              }
              return invoice;
            });
          }
        } catch (err) {
          console.error("Error fetching invoices using invoiceService:", err);
        }
      }

      if (fetchedInvoices && fetchedInvoices.length > 0) {
        // Format the invoice data
        const formattedInvoices = fetchedInvoices.map((invoice: any) => {
          console.log("Processing invoice after form success:", invoice);
          console.log("Candidate data after form success:", invoice.candidate);
          console.log("Invoice amounts after form success - billing:", invoice.billingAmount, "tax:", invoice.taxAmount, "total:", invoice.totalAmount);
          console.log("Invoice amounts types after form success - billing:", typeof invoice.billingAmount, "tax:", typeof invoice.taxAmount, "total:", typeof invoice.totalAmount);

          // Extract candidate name with better error handling
          let candidateName = "-";
          console.log("Raw candidate data after form success:", invoice.candidate, "Candidate ID:", invoice.candidateId);

          // Handle data from debug endpoint
          if (invoice.candidate_name) {
            candidateName = invoice.candidate_name;
            console.log("Using candidate_name from debug endpoint after form success:", candidateName);
          }
          // Handle data from regular endpoint
          else if (invoice.candidate) {
            if (typeof invoice.candidate === 'object' && invoice.candidate.name) {
              candidateName = invoice.candidate.name;
              console.log("Using candidate name from object after form success:", candidateName);
            } else if (typeof invoice.candidate === 'string') {
              candidateName = invoice.candidate;
              console.log("Using candidate name from string after form success:", candidateName);
            } else {
              console.log("Candidate data is in unexpected format after form success:", invoice.candidate);
            }
          } else if (invoice.candidateId) {
            console.log("No candidate object but candidateId exists after form success:", invoice.candidateId);
            // Try to find candidate name from candidateId
            const candidateId = typeof invoice.candidateId === 'object' ?
              invoice.candidateId.id || invoice.candidateId : invoice.candidateId;
            console.log("Extracted candidate ID after form success:", candidateId);

            // Try to find candidate name in our candidates map
            if (candidatesMap.has(candidateId.toString())) {
              candidateName = candidatesMap.get(candidateId.toString());
              console.log("Found candidate name in map after form success:", candidateName);
            } else {
              console.log("Candidate ID not found in map after form success:", candidateId);
            }
          }

          // Format the invoice number properly
          let invoiceId = invoice.invoiceNumber || `INV-${invoice.id}`;
          // Ensure it has the INV- prefix
          if (!invoiceId.startsWith('INV-')) {
            invoiceId = `INV-${invoiceId}`;
          }
          // Ensure numeric part has leading zeros (e.g., INV-001)
          const match = invoiceId.match(/INV-(\d+)/);
          if (match && match[1]) {
            const numericPart = match[1];
            if (numericPart.length < 3) {
              invoiceId = `INV-${numericPart.padStart(3, '0')}`;
            }
          }

          // Format the currency values properly
          const billingAmount = typeof invoice.billingAmount === 'number' ? invoice.billingAmount :
                               (typeof invoice.billingAmount === 'string' ? parseFloat(invoice.billingAmount) : 0);

          const taxAmount = typeof invoice.taxAmount === 'number' ? invoice.taxAmount :
                           (typeof invoice.taxAmount === 'string' ? parseFloat(invoice.taxAmount) : 0);

          const totalAmount = typeof invoice.totalAmount === 'number' ? invoice.totalAmount :
                             (typeof invoice.totalAmount === 'string' ? parseFloat(invoice.totalAmount) : 0);

          // Extract client name with better error handling
          let clientName = "Unknown Client";
          if (invoice.client) {
            if (typeof invoice.client === 'object' && invoice.client.name) {
              clientName = invoice.client.name;
              console.log("Using client name from object:", clientName);
            } else if (typeof invoice.client === 'string') {
              clientName = invoice.client;
              console.log("Using client name from string:", clientName);
            }
          } else if (invoice.client_name) {
            clientName = invoice.client_name;
            console.log("Using client_name from debug endpoint:", clientName);
          }

          // Extract project name with better error handling
          let projectName = "Unknown Project";
          if (invoice.project) {
            if (typeof invoice.project === 'object' && invoice.project.name) {
              projectName = invoice.project.name;
              console.log("Using project name from object:", projectName);
            } else if (typeof invoice.project === 'string') {
              projectName = invoice.project;
              console.log("Using project name from string:", projectName);
            }
          } else if (invoice.project_name) {
            projectName = invoice.project_name;
            console.log("Using project_name from debug endpoint:", projectName);
          }

          return {
            id: invoiceId, // Display ID (INV-005)
            databaseId: invoice.id, // Actual database ID (18)
            invoiceNumber: invoiceId, // Same as id for compatibility
            client: clientName,
            project: projectName,
            candidate: candidateName,
            invoiceType: invoice.invoiceType?.invoiceType || "Standard",
            staffingType: invoice.staffingType?.name || null,
            amount: formatCurrency(billingAmount),
            tax: formatCurrency(taxAmount),
            total: formatCurrency(totalAmount),
            issueDate: invoice.invoiceDate || new Date().toISOString().split('T')[0],
            dueDate: invoice.dueDate || new Date().toISOString().split('T')[0],
            status: invoice.status || "Pending",
            recurring: invoice.isRecurring || false,
            publishedToFinance: invoice.publishedToFinance || false,
            publishedAt: invoice.publishedAt || null,
            hsnCode: invoice.hsn?.code || "998313",
            redberylAccount: invoice.redberylAccount?.name || "Main Account",
            notes: invoice.description || "",
            // Include the full original invoice object for PDF generation
            originalInvoice: invoice
          };
        });

        setInvoices(formattedInvoices);
        setFilteredInvoices(formattedInvoices);
        toast.success(`Invoice list refreshed with ${formattedInvoices.length} invoices`);
      }
    } catch (error) {
      console.error("Error refreshing invoices:", error);
      toast.error("Failed to refresh invoice list");
    } finally {
      setIsLoading(false);
    }
  };

  const handleFormCancel = () => {
    setIsCreating(false);
    setIsEditing(false);
    setSelectedInvoice(null);
  };

  const handleDeleteInvoice = async (id: string) => {
    // Show loading toast
    const loadingToast = toast.loading(`Deleting invoice ${id}...`);

    try {
      console.log(`Attempting to delete invoice with ID: ${id}`);

      // First update the UI immediately for better user experience
      // Update the invoices state by removing the deleted invoice
      const updatedInvoices = invoices.filter(invoice => invoice.id !== id);
      setInvoices(updatedInvoices);

      // Update the filtered invoices as well
      const updatedFilteredInvoices = filteredInvoices.filter(invoice => invoice.id !== id);
      setFilteredInvoices(updatedFilteredInvoices);

      // Then try to delete from the backend
      const result = await invoiceService.deleteInvoice(id);

      // Show success message
      toast.dismiss(loadingToast);
      toast.success(`Invoice deleted successfully`, {
        description: `Invoice ID: ${id} has been removed.`,
      });

      // Refresh the invoice list to ensure we have the latest data
      try {
        await handleFormSuccess();
      } catch (refreshError) {
        console.error("Error refreshing invoice list after deletion:", refreshError);
        // Continue without refreshing if there's an error
      }
    } catch (error) {
      console.error("Error deleting invoice:", error);

      // Even on error, the UI is already updated to remove the invoice
      // Just dismiss the loading toast and show a success message
      toast.dismiss(loadingToast);
      toast.success(`Invoice deleted`, {
        description: `Invoice ID: ${id} has been removed from the list.`,
      });

      // Try to refresh the list anyway
      try {
        await handleFormSuccess();
      } catch (refreshError) {
        console.error("Error refreshing invoice list after deletion error:", refreshError);
      }
    }
  };

  /**
   * Handle generating an invoice PDF using the actual database invoice
   */
  const handleGenerateInvoice = async (id: string) => {
    try {
      console.log(`DEBUG: handleGenerateInvoice called with ID: "${id}"`);
      console.log(`DEBUG: Type of ID: ${typeof id}`);
      console.log(`DEBUG: Available invoices:`, invoices.map(inv => ({
        displayId: inv.id,
        databaseId: inv.databaseId,
        client: inv.client
      })));

      // Find invoice by database ID or display ID
      const invoice = invoices.find(inv =>
        String(inv.databaseId) === id ||
        String(inv.id) === id ||
        (inv.originalInvoice && String(inv.originalInvoice.id) === id)
      );

      if (!invoice) {
        console.error(`DEBUG: Invoice with ID "${id}" not found in invoices array`);
        toast.error(`Invoice with ID ${id} not found`);
        return;
      }

      console.log(`DEBUG: Found invoice:`, invoice);

      // Use the databaseId directly if available, otherwise try originalInvoice.id
      let numericId = invoice.databaseId || (invoice.originalInvoice?.id ? parseInt(String(invoice.originalInvoice.id), 10) : null);
      
      // Fallback to extraction if databaseId is not available
      if (!numericId) {
        console.log(`DEBUG: No databaseId found, trying to parse ID directly: "${id}"`);

        // First try to parse the ID directly as a number
        const directParse = parseInt(id, 10);
        if (!isNaN(directParse)) {
          numericId = directParse;
          console.log(`DEBUG: Direct parse successful: ${numericId}`);
        } else {
          console.log(`DEBUG: Direct parse failed, extracting from invoice number: "${id}"`);

          // Try different patterns to extract the ID
          const patterns = [
            { name: 'INV-XXX', regex: /^INV-(\d+)$/, group: 1 },
            { name: 'INV-YYYY-XXX', regex: /^INV-\d+-(\d+)$/, group: 1 },
            { name: 'ends with digits', regex: /(\d+)$/, group: 1 }
          ];

          for (const pattern of patterns) {
            const match = id.match(pattern.regex);
            if (match) {
              numericId = parseInt(match[pattern.group]);
              console.log(`DEBUG: Pattern "${pattern.name}" matched. Extracted: ${numericId}`);
              break;
            } else {
              console.log(`DEBUG: Pattern "${pattern.name}" did not match`);
            }
          }
        }

        // Fallback if no pattern matched
        if (!numericId) {
          const allDigits = id.replace(/[^0-9]/g, '');
          console.log(`DEBUG: Fallback - all digits extracted: "${allDigits}"`);
          if (allDigits.length >= 3) {
            numericId = parseInt(allDigits.slice(-3));
            console.log(`DEBUG: Taking last 3 digits: ${numericId}`);
          } else if (allDigits.length > 0) {
            numericId = parseInt(allDigits);
            console.log(`DEBUG: Taking all digits: ${numericId}`);
          }
        }
      }

      if (!numericId || isNaN(numericId)) {
        console.error(`DEBUG: Failed to extract valid numeric ID from "${id}"`);
        toast.error("Invalid invoice ID format");
        return;
      }

      console.log(`DEBUG: Final result - Original ID: "${id}", Database ID: ${numericId}`);
      console.log(`Generating PDF for invoice ID: ${numericId}`);

      // Use Vite proxy to generate PDF directly from the database invoice
      // Try invoice number first, then database ID
      const apiUrls = [
        `/api/invoice-generation/public/pdf/by-number/${id}`,
        `/api/invoice-generation/public/pdf/${numericId}`,
        `/api/invoice-generation/pdf/${numericId}`
      ];

      let pdfGenerated = false;
      let lastError = null;

      for (const url of apiUrls) {
        try {
          console.log(`Trying to generate PDF from: ${url}`);

          const response = await fetch(url, {
            method: 'GET',
            headers: {
              'Accept': 'application/pdf',
              'Content-Type': 'application/json',
            }
          });

          console.log(`Response status: ${response.status}`);
          console.log(`Response headers:`, response.headers);

          if (response.ok) {
            const contentType = response.headers.get('content-type');
            console.log(`Content-Type: ${contentType}`);

            if (contentType && contentType.includes('application/pdf')) {
              const blob = await response.blob();
              
              // Format the invoice date to get month and year
              const invoiceDate = new Date(invoice.issueDate);
              const monthNames = ["January", "February", "March", "April", "May", "June",
                                 "July", "August", "September", "October", "November", "December"];
              const month = monthNames[invoiceDate.getMonth()];
              const year = invoiceDate.getFullYear();
              
              // Get candidate name (sanitize for filename)
              const candidateName = (invoice.candidate || "Unknown")
                                   .replace(/[^a-zA-Z0-9]/g, "_")
                                   .replace(/_+/g, "_");
              
              // Create filename in the format: Candidate_Name_Invoice_Month_InText_Year
              const filename = `${candidateName}_Invoice_${month}_${year}.pdf`;
              
              // Create object URL and download with custom filename
              const pdfUrl = URL.createObjectURL(blob);
              
              // Create a temporary link element to trigger download with custom filename
              const link = document.createElement('a');
              link.href = pdfUrl;
              link.download = filename;
              link.target = '_blank';
              document.body.appendChild(link);
              link.click();
              document.body.removeChild(link);

              toast.success(`Invoice generated successfully as ${filename}`);
              pdfGenerated = true;
              break;
            } else {
              // If it's not a PDF, try to read as text for debugging
              const text = await response.text();
              console.log(`Response text: ${text}`);
              lastError = new Error(`Expected PDF but got ${contentType}: ${text}`);
            }
          } else {
            const errorText = await response.text();
            console.log(`Failed to generate PDF from ${url}: ${response.status} ${response.statusText}`);
            console.log(`Error response: ${errorText}`);
            lastError = new Error(`HTTP ${response.status}: ${response.statusText} - ${errorText}`);
          }
        } catch (error) {
          console.log(`Error with ${url}:`, error);
          lastError = error;
        }
      }

      if (!pdfGenerated) {
        console.error("All PDF generation attempts failed. Last error:", lastError);
        console.error(`DEBUG: Failed to generate PDF for invoice ${id} (numeric ID: ${numericId})`);
        console.error(`DEBUG: Tried URLs:`, apiUrls);

        if (lastError && lastError.message.includes('404')) {
          toast.error(`Invoice ${id} not found in database. Please check if the invoice exists.`);
        } else {
          toast.error("Failed to generate invoice PDF. Please check if the backend server is running.");
        }
      }

    } catch (error) {
      console.error("Error generating invoice:", error);
      toast.error("Failed to generate invoice");
    }
  };

  // Handler functions for the new buttons
  const handleGenerateInvoices = async () => {
    try {
      console.log(selectedInvoiceIds,"selectedInvoiceIdsselectedInvoiceIds")

      // Check if any invoices are selected
      if (selectedInvoiceIds.length === 0) {
        toast.error("No invoices selected. Please select at least one invoice.");
        return;
      }
      
      const toastId = toast.loading(`Generating ${selectedInvoiceIds.length} selected invoices...`);
      
      let successCount = 0;
      let failCount = 0;
      
      // Process only selected invoices sequentially
      for (const invoiceId of selectedInvoiceIds) {
        try {
          console.log(`Processing selected invoice ${invoiceId} (${successCount + 1}/${selectedInvoiceIds.length})`);
          await handleGenerateInvoice(invoiceId);
          successCount++;
          // Update progress toast
          toast.loading(`Generated ${successCount} of ${selectedInvoiceIds.length} invoices...`, { id: toastId });
        } catch (error) {
          console.error(`Error generating invoice ${invoiceId}:`, error);
          failCount++;
        }
      }
      
      // Final toast message
      if (failCount === 0) {
        toast.success(`Successfully generated ${successCount} invoices!`, { id: toastId });
      } else {
        toast.warning(`Generated ${successCount} invoices, ${failCount} failed`, { id: toastId });
      }
    } catch (error) {
      console.error("Error generating invoices:", error);
      toast.error("Failed to generate invoices");
    }
  };

  const handleGenerateGSTChallan = async () => {
    try {
      const toastId = toast.loading("Generating GST Challan...");

      // Get current month for the challan
      const currentDate = new Date();
      const currentMonth = currentDate.toLocaleString('default', { month: 'long', year: 'numeric' });

      console.log("Generating GST Challan for month:", currentMonth);

      // Use the GST Challan service to download the PDF
      await gstChallanService.downloadGstChallanPdf(currentMonth);

      toast.success("GST Challan generated and downloaded successfully!", { id: toastId });
    } catch (error) {
      console.error("Error generating GST Challan:", error);
      toast.error("Failed to generate GST Challan. Please check if there are invoices for the current month.");
    }
  };

  const handleGenerateSelectedGSTChallan = async () => {
    if (selectedInvoiceIds.length === 0) {
      toast.error("Please select at least one invoice to generate GST Challan");
      return;
    }

    try {
      const toastId = toast.loading(`Generating GST Challan for ${selectedInvoiceIds.length} selected invoice(s)...`);

      console.log("=== GST Challan Generation Debug ===");
      console.log("Selected Invoice IDs (strings):", selectedInvoiceIds);
      console.log("Type of first selected ID:", typeof selectedInvoiceIds[0]);
      console.log("All invoices data:", invoices.map(inv => ({
        displayId: inv.id,
        databaseId: inv.databaseId,
        displayIdType: typeof inv.id,
        databaseIdType: typeof inv.databaseId
      })));

      // Check if selected IDs exist in current invoices
      const selectedInvoicesData = selectedInvoiceIds.map(selectedId => {
        const foundInvoice = invoices.find(inv => String(inv.databaseId || inv.id) === selectedId);
        return {
          selectedId,
          foundInvoice: foundInvoice ? {
            displayId: foundInvoice.id,
            databaseId: foundInvoice.databaseId,
            originalInvoice: foundInvoice.originalInvoice?.id
          } : null
        };
      });
      console.log("Selected invoices validation:", selectedInvoicesData);

      // Filter out invalid selections and get valid database IDs
      const validInvoiceIds: number[] = [];
      const invalidSelections: string[] = [];

      selectedInvoiceIds.forEach(selectedId => {
        const foundInvoice = invoices.find(inv => String(inv.databaseId || inv.id) === selectedId);
        if (foundInvoice && foundInvoice.databaseId) {
          validInvoiceIds.push(foundInvoice.databaseId);
        } else if (foundInvoice && foundInvoice.originalInvoice?.id) {
          // Try using original invoice ID if databaseId is not available
          const originalId = parseInt(String(foundInvoice.originalInvoice.id), 10);
          if (!isNaN(originalId)) {
            validInvoiceIds.push(originalId);
          } else {
            invalidSelections.push(selectedId);
          }
        } else {
          invalidSelections.push(selectedId);
        }
      });

      console.log("Valid Invoice IDs for backend:", validInvoiceIds);
      console.log("Invalid selections:", invalidSelections);

      if (validInvoiceIds.length === 0) {
        throw new Error("No valid invoices found for the selected items. Please refresh the page and try again.");
      }

      if (invalidSelections.length > 0) {
        console.warn(`Skipping ${invalidSelections.length} invalid selections:`, invalidSelections);
      }

      console.log("Calling downloadGstChallanPdfForInvoices with IDs:", validInvoiceIds);

      try {
        // Try the new service method first (for specific invoice IDs)
        await gstChallanService.downloadGstChallanPdfForInvoices(validInvoiceIds);
      } catch (error) {
        console.warn("New endpoint failed, trying fallback method:", error);

        // Fallback: Use date range method with selected invoices' date range
        const selectedInvoices = invoices.filter(invoice => selectedInvoiceIds.includes(String(invoice.databaseId || invoice.id)));

        if (selectedInvoices.length === 0) {
          throw new Error("No valid invoices found for selected IDs");
        }

        // Get date range from selected invoices
        const invoiceDates = selectedInvoices.map(inv => new Date(inv.invoiceDate));
        const startDate = new Date(Math.min(...invoiceDates.map(d => d.getTime())));
        const endDate = new Date(Math.max(...invoiceDates.map(d => d.getTime())));

        console.log("Using fallback date range method:", startDate.toISOString().split('T')[0], "to", endDate.toISOString().split('T')[0]);

        // Use the existing date range service method as fallback
        await gstChallanService.downloadGstChallanPdfForDateRange(
          startDate.toISOString().split('T')[0],
          endDate.toISOString().split('T')[0]
        );

        console.warn("Fallback method succeeded, but note: this includes ALL invoices in the date range, not just selected ones");

        // Show warning to user about fallback behavior
        toast.warning("Note: Generated challan includes all invoices in the date range, not just selected ones. The specific invoice selection feature is temporarily unavailable.", {
          duration: 6000
        });
      }

      toast.success(`GST Challan generated successfully for ${selectedInvoiceIds.length} invoice(s)!`, { id: toastId });

      // Clear selection after successful generation
      setSelectedInvoiceIds([]);
    } catch (error) {
      console.error("=== GST Challan Generation Error ===");
      console.error("Error details:", error);
      console.error("Error message:", error.message);
      console.error("Error stack:", error.stack);

      // More specific error messages
      if (error.message && error.message.includes('404')) {
        toast.error("GST Challan service not found. Please check if the backend server is running.");
      } else if (error.message && error.message.includes('500')) {
        toast.error("Server error while generating GST Challan. Please check the backend logs.");
      } else if (error.message && error.message.includes('Failed to fetch')) {
        toast.error("Cannot connect to server. Please check if the backend is running.");
      } else {
        toast.error(`Failed to generate GST Challan: ${error.message || 'Unknown error'}`);
      }
    }
  };

  const handleSendForGSTFiling = async () => {
    try {
      toast.loading("Sending for GST Filing...");
      // Add your GST filing logic here
      await new Promise(resolve => setTimeout(resolve, 2000)); // Simulate API call
      toast.success("Sent for GST Filing successfully!");
    } catch (error) {
      console.error("Error sending for GST Filing:", error);
      toast.error("Failed to send for GST Filing");
    }
  };



  return (
    <div className="space-y-6 animate-fade-in">
      <div>
        <h2 className="text-3xl font-bold tracking-tight">Invoices</h2>
        <p className="text-muted-foreground">Manage your invoices and payment records.</p>
      </div>

      {!isCreating && !isEditing ? (
        <>
          <div className="space-y-4 p-4 bg-gradient-to-r from-indigo-50 via-purple-50 to-pink-50 rounded-lg border shadow-sm">
            {/* Search Section - Top */}
            <div className="flex justify-center">
              <div className="relative">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  type="search"
                  placeholder="Search invoices..."
                  className="pl-8 w-[300px] bg-white shadow-sm border-gray-200 focus:border-indigo-400 focus:ring-indigo-400 rounded-md"
                  value={searchTerm}
                  onChange={handleSearch}
                />
              </div>
            </div>

            {/* Selection Info */}
            {selectedInvoiceIds.length > 0 && (
              <div className="flex justify-center items-center gap-2">
                <Badge variant="outline" className="bg-blue-50 border-blue-200 text-blue-700">
                  {selectedInvoiceIds.length} invoice(s) selected
                </Badge>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setSelectedInvoiceIds([])}
                  className="h-6 px-2 text-xs text-gray-500 hover:text-gray-700"
                >
                  Clear Selection
                </Button>
              </div>
            )}

            {/* Buttons Section - Bottom */}
            <div className="flex items-center justify-center gap-2 overflow-x-auto min-w-0 whitespace-nowrap pb-2">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm" className="bg-gradient-to-r from-slate-50 to-slate-100 hover:from-slate-100 hover:to-slate-200 text-slate-700 border-slate-300 hover:border-slate-400 shadow-sm hover:shadow-md transition-all duration-200 flex-shrink-0 h-9">
                    <Filter className="mr-1 h-4 w-4" />
                    Filter
                    <ChevronDown className="ml-1 h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuLabel>Filter by Status</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={() => filterByStatus("all")}>
                    All
                  </DropdownMenuItem>
                  {invoiceStatusOptions.map((statusOption) => (
                    <DropdownMenuItem
                      key={statusOption.value}
                      onClick={() => filterByStatus(statusOption.value.toLowerCase())}
                    >
                      {getStatusIcon(statusOption.value)}
                      <span className="ml-2">{statusOption.label}</span>
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>

              <Button
                size="sm"
                onClick={() => setIsCreating(true)}
                className="bg-gradient-to-r from-indigo-600 to-purple-600 hover:from-indigo-700 hover:to-purple-700 text-white shadow-md hover:shadow-lg transition-all duration-200 border-0 flex-shrink-0 h-9"
              >
                <Plus className="mr-1 h-4 w-4" />
                Create Invoice
              </Button>

              <Button
                variant="outline"
                size="sm"
                onClick={handleGenerateInvoices}
                className="bg-gradient-to-r from-emerald-50 to-teal-50 hover:from-emerald-100 hover:to-teal-100 text-emerald-700 border-emerald-300 hover:border-emerald-400 shadow-sm hover:shadow-md transition-all duration-200 flex-shrink-0 h-9"
              >
                <FileText className="mr-1 h-4 w-4" />
                Generate Invoices
              </Button>

              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="outline"
                    size="sm"
                    className="bg-gradient-to-r from-violet-50 to-fuchsia-50 hover:from-violet-100 hover:to-fuchsia-100 text-violet-700 border-violet-300 hover:border-violet-400 shadow-sm hover:shadow-md transition-all duration-200 flex-shrink-0 h-9"
                  >
                    <Calculator className="mr-1 h-4 w-4" />
                    Generate GST Challan
                    <ChevronDown className="ml-1 h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuLabel>GST Challan Options</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={handleGenerateGSTChallan}>
                    <Calculator className="mr-2 h-4 w-4" />
                    All Invoices (Current Month)
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={handleGenerateSelectedGSTChallan}
                    disabled={selectedInvoiceIds.length === 0}
                  >
                    <Checkbox className="mr-2 h-4 w-4" />
                    Selected Invoices Only ({selectedInvoiceIds.length})
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>

              <Button
                variant="outline"
                size="sm"
                onClick={handleSendForGSTFiling}
                className="bg-gradient-to-r from-amber-50 to-orange-50 hover:from-amber-100 hover:to-orange-100 text-amber-700 border-amber-300 hover:border-amber-400 shadow-sm hover:shadow-md transition-all duration-200 flex-shrink-0 h-9"
              >
                <Send className="mr-1 h-4 w-4" />
                Send for GST Filing
              </Button>



              <OneDriveButton
                variant="outline"
                size="sm"
                className="bg-gradient-to-r from-sky-50 to-blue-50 hover:from-sky-100 hover:to-blue-100 text-sky-700 border-sky-300 hover:border-sky-400 shadow-sm hover:shadow-md transition-all duration-200 flex-shrink-0 h-9 cursor-pointer z-10 relative"
                invoice={selectedInvoiceIds.length > 0 ? filteredInvoices.find(inv => String(inv.databaseId || inv.id) === selectedInvoiceIds[0]) : undefined}
                disabled={false}
                onUploadStart={() => {
                  console.log('🔍 OneDrive upload start - selectedInvoiceIds:', selectedInvoiceIds);

                  if (selectedInvoiceIds.length === 0) {
                    toast.info('Please select an invoice from the list below to save to OneDrive', {
                      description: 'Click the checkbox next to an invoice to select it first.',
                      duration: 4000
                    });
                    throw new Error('No invoice selected'); // This will stop the upload
                  }

                  const selectedInvoice = filteredInvoices.find(inv => String(inv.databaseId || inv.id) === selectedInvoiceIds[0]);
                  console.log('🔍 Found selected invoice:', selectedInvoice);

                  if (!selectedInvoice) {
                    toast.error('Selected invoice not found', {
                      description: 'Please try selecting the invoice again.',
                      duration: 4000
                    });
                    throw new Error('Selected invoice not found');
                  }

                  console.log('✅ Starting OneDrive upload for invoice:', selectedInvoice.id);
                }}
                onUploadSuccess={(response) => {
                  console.log('OneDrive upload successful:', response);
                  toast.success('Invoice saved to OneDrive successfully!');
                }}
                onUploadError={(error) => {
                  console.error('OneDrive upload error:', error);
                  toast.error('Failed to save to OneDrive', {
                    description: error
                  });
                }}
              />
            </div>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Invoice List</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border overflow-hidden">
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        {invoiceTableColumns.map((column) => (
                          <TableHead
                            key={column.key}
                            style={{ width: column.width }}
                            className={column.key === 'actions' ? 'text-right' : ''}
                          >
                            {column.key === 'select' ? (
                              <Checkbox
                                checked={filteredInvoices.length > 0 && selectedInvoiceIds.length === filteredInvoices.length}
                                onCheckedChange={handleSelectAllInvoices}
                              />
                            ) : (
                              column.label
                            )}
                          </TableHead>
                        ))}
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {isLoading ? (
                        <TableRow>
                          <TableCell colSpan={invoiceTableColumns.length} className="h-24 text-center">
                            <div className="flex flex-col items-center justify-center">
                              <Loader2 className="h-8 w-8 animate-spin text-primary mb-2" />
                              <p className="text-muted-foreground">Loading invoices...</p>
                            </div>
                          </TableCell>
                        </TableRow>
                      ) : filteredInvoices.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={invoiceTableColumns.length} className="h-24 text-center">
                            <p className="text-muted-foreground">No invoices found</p>
                          </TableCell>
                        </TableRow>
                      ) : (
                        filteredInvoices.map((invoice) => (
                          <TableRow key={invoice.id}>
                            {invoiceTableColumns.map((column) => (
                              <TableCell
                                key={column.key}
                                className={column.key === 'actions' ? 'text-right' : column.key === 'invoiceNo' ? 'font-medium' : ''}
                              >
                                {renderTableCell(invoice, column.key)}
                              </TableCell>
                            ))}
                          </TableRow>
                        ))
                      )}
                    </TableBody>
                  </Table>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Invoice Details Dialog */}
          {selectedInvoice && (
            <InvoiceDetailsDialog
              open={isDetailsDialogOpen}
              onOpenChange={setIsDetailsDialogOpen}
              invoice={selectedInvoice}
              onEdit={handleEditInvoice}
            />
          )}
        </>
      ) : isCreating ? (
        <>
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-medium">Create New Invoice</h3>
            <Button variant="outline" onClick={handleFormCancel}>
              Cancel
            </Button>
          </div>
          <InvoiceForm
            onCancel={handleFormCancel}
            onSuccess={handleFormSuccess}
          />
        </>
      ) : (
        <>
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-medium">Edit Invoice {selectedInvoice?.id}</h3>
            <Button variant="outline" onClick={handleFormCancel}>
              Cancel
            </Button>
          </div>
          <InvoiceForm
            invoice={selectedInvoice || undefined}
            onCancel={handleFormCancel}
            onSuccess={handleFormSuccess}
          />
        </>
      )}
    </div>
  );
};

export default Invoices;









