@echo off
echo Testing Invoice CRUD Operations...

echo.
echo ===== Testing Backend Connection =====
curl -s http://************:8091/invoices
if %errorlevel% equ 0 (
    echo Backend is running!
) else (
    echo Backend is not running. Please start it first.
    echo Run: cd backend && mvn spring-boot:run
    pause
    exit /b 1
)

echo.
echo ===== Testing Invoice Endpoints =====

echo.
echo --- Testing GET /invoices ---
curl -s http://************:8091/invoices

echo.
echo --- Testing GET /invoices/getAll ---
curl -s http://************:8091/invoices/getAll

echo.
echo --- Testing GET /api/invoices ---
curl -s http://************:8091/api/invoices

echo.
echo --- Testing GET /api/invoices/getAll ---
curl -s http://************:8091/api/invoices/getAll

echo.
echo ===== Testing Invoice Creation =====
curl -X POST http://************:8091/invoices ^
  -H "Content-Type: application/json" ^
  -H "Authorization: Basic YWRtaW46YWRtaW4xMjM=" ^
  -d "{\"invoiceNumber\":\"TEST-001\",\"clientId\":1,\"amount\":10000,\"description\":\"Test invoice\"}"

echo.
echo ===== Invoice Test Complete =====
echo All operations tested. Check the responses above.
echo.
echo Frontend is available at: http://************:3060/invoices
echo.
pause
