/**
 * Invoice CRUD Test Utility
 * 
 * Comprehensive testing for invoice operations
 */

import { toast } from "sonner";

// Get server URL from environment
const getServerUrl = () => {
  return import.meta.env.VITE_API_BASE_URL || 'http://192.168.1.30:8091';
};

// Helper function to make authenticated requests
const makeAuthenticatedRequest = async (url: string, options: RequestInit = {}) => {
  const serverUrl = getServerUrl();
  const fullUrl = url.startsWith('http') ? url : `${serverUrl}${url}`;
  
  const defaultHeaders = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'Authorization': 'Basic ' + btoa('admin:admin123')
  };

  const requestOptions: RequestInit = {
    ...options,
    headers: {
      ...defaultHeaders,
      ...options.headers
    }
  };

  console.log(`Making request to: ${fullUrl}`, requestOptions);
  
  const response = await fetch(fullUrl, requestOptions);
  
  if (!response.ok) {
    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
  }
  
  return response;
};

/**
 * Test invoice creation
 */
export const testInvoiceCreate = async (): Promise<boolean> => {
  try {
    console.log('🧪 Testing Invoice Creation...');
    
    const testInvoice = {
      invoiceNumber: `TEST-${Date.now()}`,
      clientId: 1,
      projectId: 1,
      invoiceTypeId: 1,
      amount: 10000,
      gstAmount: 1800,
      totalAmount: 11800,
      description: 'Test invoice for CRUD operations',
      invoiceDate: new Date().toISOString().split('T')[0],
      dueDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
    };

    const response = await makeAuthenticatedRequest('/invoices', {
      method: 'POST',
      body: JSON.stringify(testInvoice)
    });

    const result = await response.json();
    console.log('✅ Invoice created successfully:', result);
    toast.success(`Invoice created with ID: ${result.id}`);
    return true;
  } catch (error) {
    console.error('❌ Invoice creation failed:', error);
    toast.error(`Invoice creation failed: ${error.message}`);
    return false;
  }
};

/**
 * Test invoice reading/fetching
 */
export const testInvoiceRead = async (): Promise<boolean> => {
  try {
    console.log('🧪 Testing Invoice Reading...');
    
    // Try multiple endpoints
    const endpoints = [
      '/invoices',
      '/invoices/getAll',
      '/api/invoices',
      '/api/invoices/getAll'
    ];

    let success = false;
    let invoices = [];

    for (const endpoint of endpoints) {
      try {
        console.log(`Trying endpoint: ${endpoint}`);
        const response = await makeAuthenticatedRequest(endpoint);
        invoices = await response.json();
        console.log(`✅ Successfully fetched ${invoices.length} invoices from ${endpoint}`);
        success = true;
        break;
      } catch (error) {
        console.log(`❌ Failed to fetch from ${endpoint}:`, error.message);
      }
    }

    if (success) {
      toast.success(`Successfully fetched ${invoices.length} invoices`);
      return true;
    } else {
      toast.error('Failed to fetch invoices from any endpoint');
      return false;
    }
  } catch (error) {
    console.error('❌ Invoice reading failed:', error);
    toast.error(`Invoice reading failed: ${error.message}`);
    return false;
  }
};

/**
 * Test invoice update
 */
export const testInvoiceUpdate = async (invoiceId: number = 1): Promise<boolean> => {
  try {
    console.log('🧪 Testing Invoice Update...');
    
    const updateData = {
      description: `Updated test invoice - ${new Date().toISOString()}`,
      amount: 15000,
      gstAmount: 2700,
      totalAmount: 17700
    };

    const response = await makeAuthenticatedRequest(`/invoices/${invoiceId}`, {
      method: 'PUT',
      body: JSON.stringify(updateData)
    });

    const result = await response.json();
    console.log('✅ Invoice updated successfully:', result);
    toast.success(`Invoice ${invoiceId} updated successfully`);
    return true;
  } catch (error) {
    console.error('❌ Invoice update failed:', error);
    toast.error(`Invoice update failed: ${error.message}`);
    return false;
  }
};

/**
 * Test invoice deletion
 */
export const testInvoiceDelete = async (invoiceId: number = 1): Promise<boolean> => {
  try {
    console.log('🧪 Testing Invoice Deletion...');
    
    const response = await makeAuthenticatedRequest(`/invoices/${invoiceId}`, {
      method: 'DELETE'
    });

    console.log('✅ Invoice deleted successfully');
    toast.success(`Invoice ${invoiceId} deleted successfully`);
    return true;
  } catch (error) {
    console.error('❌ Invoice deletion failed:', error);
    toast.error(`Invoice deletion failed: ${error.message}`);
    return false;
  }
};

/**
 * Test backend connectivity
 */
export const testBackendConnectivity = async (): Promise<boolean> => {
  try {
    console.log('🧪 Testing Backend Connectivity...');
    
    const serverUrl = getServerUrl();
    console.log(`Testing connection to: ${serverUrl}`);
    
    // Try a simple health check or basic endpoint
    const response = await fetch(`${serverUrl}/invoices`, {
      method: 'GET',
      headers: {
        'Authorization': 'Basic ' + btoa('admin:admin123')
      }
    });

    if (response.ok) {
      console.log('✅ Backend is accessible');
      toast.success('Backend connection successful');
      return true;
    } else {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }
  } catch (error) {
    console.error('❌ Backend connectivity failed:', error);
    toast.error(`Backend connection failed: ${error.message}`);
    return false;
  }
};

/**
 * Run comprehensive CRUD test suite
 */
export const runInvoiceCrudTests = async (): Promise<void> => {
  console.log('🚀 Starting Invoice CRUD Test Suite...');
  toast.info('Starting Invoice CRUD tests...');

  const results = {
    connectivity: false,
    read: false,
    create: false,
    update: false,
    delete: false
  };

  // Test 1: Backend Connectivity
  results.connectivity = await testBackendConnectivity();
  
  if (!results.connectivity) {
    toast.error('Backend not accessible. Please start the backend server.');
    return;
  }

  // Test 2: Read invoices
  results.read = await testInvoiceRead();

  // Test 3: Create invoice
  results.create = await testInvoiceCreate();

  // Test 4: Update invoice (if create was successful)
  if (results.create) {
    await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second
    results.update = await testInvoiceUpdate();
  }

  // Test 5: Delete invoice (if create was successful)
  if (results.create) {
    await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second
    results.delete = await testInvoiceDelete();
  }

  // Summary
  const passedTests = Object.values(results).filter(Boolean).length;
  const totalTests = Object.keys(results).length;
  
  console.log('📊 Test Results:', results);
  
  if (passedTests === totalTests) {
    toast.success(`🎉 All ${totalTests} tests passed! Invoice CRUD is working perfectly.`);
  } else {
    toast.warning(`⚠️ ${passedTests}/${totalTests} tests passed. Some issues need attention.`);
  }
};
