@echo off
echo ========================================
echo FIXING NETWORK ACCESS FOR PROJECTS
echo ========================================
echo.

echo Problem: "Error loading projects" when accessing from IP address
echo Solution: Fix CORS configuration and ensure consistent API endpoints
echo.

echo ========================================
echo Step 1: Restart Backend with Fixed CORS
echo ========================================
echo.

echo Stopping any existing backend processes...
taskkill /f /im java.exe 2>nul
timeout /t 3 /nobreak >nul

echo Starting backend with updated CORS settings...
cd backend

echo Compiling backend...
mvn clean compile -q
if %ERRORLEVEL% neq 0 (
    echo ERROR: Backend compilation failed!
    pause
    exit /b 1
)

echo Starting backend server...
start "Invoice Manager Backend - Network Fixed" cmd /k "echo Backend starting with network access... && mvn spring-boot:run"

echo Waiting for backend to start...
timeout /t 15 /nobreak >nul

cd ..

echo ========================================
echo Step 2: Test API from Network IP
echo ========================================
echo.

echo Testing projects API from network IP...
powershell -Command "try { $response = Invoke-RestMethod -Uri 'http://************:8091/projects' -Method Get; Write-Host 'SUCCESS: API accessible from network IP'; Write-Host 'Projects found:' $response.Length } catch { Write-Host 'ERROR: API not accessible from network IP -' $_.Exception.Message }"

echo.
echo ========================================
echo Step 3: Test CORS Headers
echo ========================================
echo.

echo Testing CORS headers...
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://************:8091/projects' -Method Options; Write-Host 'CORS Headers:'; $response.Headers | Format-Table } catch { Write-Host 'CORS test failed:' $_.Exception.Message }"

echo.
echo ========================================
echo Step 4: Start Frontend
echo ========================================
echo.

echo Starting frontend with network configuration...
cd frontend

echo Installing dependencies (if needed)...
call npm install --silent

echo Starting frontend server...
start "Invoice Manager Frontend - Network Access" cmd /k "echo Frontend starting for network access... && npm run dev"

echo Waiting for frontend to start...
timeout /t 10 /nobreak >nul

cd ..

echo.
echo ========================================
echo Step 5: Test Network Access
echo ========================================
echo.

echo Testing frontend accessibility...
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:3060' -Method Get -TimeoutSec 5; Write-Host 'Frontend accessible on localhost:3060' } catch { Write-Host 'Frontend not yet ready on localhost:3060' }"

echo.
echo ========================================
echo NETWORK ACCESS TEST URLS
echo ========================================
echo.

echo From the SAME PC (localhost):
echo   http://localhost:3060/simple-projects
echo   http://localhost:3060/test-projects
echo.

echo From OTHER PCs on network:
echo   http://************:3060/simple-projects
echo   http://************:3060/test-projects
echo.

echo Direct API test from any PC:
echo   http://************:8091/projects
echo.

echo ========================================
echo EXPECTED RESULTS
echo ========================================
echo.

echo ✅ From localhost (same PC):
echo   - Simple Projects page loads
echo   - Shows project data in table
echo   - No CORS errors in console
echo.

echo ✅ From network IP (other PCs):
echo   - Simple Projects page loads
echo   - Shows same project data
echo   - No "Failed to fetch" errors
echo   - No CORS errors in browser console
echo.

echo ✅ Direct API test:
echo   - Returns JSON data with status 200
echo   - No CORS errors
echo   - Accessible from any PC on network
echo.

echo ========================================
echo TROUBLESHOOTING
echo ========================================
echo.

echo If still getting "Failed to fetch" from network IP:
echo.

echo 1. Check Windows Firewall:
echo    - Allow Java through firewall
echo    - Allow Node.js through firewall
echo    - Allow ports 8091 and 3060
echo.

echo 2. Check Network Settings:
echo    - Ensure ************ is correct IP
echo    - Test ping from other PC: ping ************
echo    - Check if ports are open: telnet ************ 8091
echo.

echo 3. Check Backend Logs:
echo    - Look for CORS errors in backend console
echo    - Verify "CrossOrigin origins = *" is loaded
echo.

echo 4. Check Browser Console:
echo    - Open F12 Developer Tools
echo    - Look for CORS or network errors
echo    - Check if API calls are going to correct IP
echo.

echo ========================================
echo WHAT WAS FIXED
echo ========================================
echo.

echo ✅ Backend CORS Configuration:
echo   - Changed from localhost-only to origins = "*"
echo   - Allows access from any IP address
echo   - Fixed PublicProjectController and InvoiceController
echo.

echo ✅ Frontend API Endpoints:
echo   - Uses VITE_API_BASE_URL environment variable
echo   - Consistent IP address usage
echo   - Proper endpoint construction
echo.

echo ✅ Environment Configuration:
echo   - .env file configured for network access
echo   - VITE_API_BASE_URL = http://************:8091
echo   - Consistent across all components
echo.

echo ========================================
echo.

echo Both backend and frontend should now be running.
echo Test the URLs above to verify network access is working.
echo.

echo If you see project data on both localhost and network IP,
echo the fix is successful!
echo.

pause
