import{g as n,a as I}from"./index-Dw6YsHTe.js";const o=($,l,t)=>{const p=n(),g=I(),h=[`http://${p}:${g}/${$}`,`http://${p}:${g}/api/${$}`,`/${$}`,`/api/${$}`];return l==="getAll"?[...h,`http://${p}:${g}/${$}/getAll`,`http://${p}:${g}/api/${$}/getAll`,`/${$}/getAll`,`/api/${$}/getAll`]:l==="getById"&&t!==void 0?[`http://${p}:${g}/${$}/${t}`,`http://${p}:${g}/api/${$}/${t}`,`http://${p}:${g}/${$}/getById/${t}`,`http://${p}:${g}/api/${$}/getById/${t}`,`/${$}/${t}`,`/api/${$}/${t}`,`/${$}/getById/${t}`,`/api/${$}/getById/${t}`]:l==="delete"&&t!==void 0?[`http://${p}:${g}/${$}/${t}`,`http://${p}:${g}/api/${$}/${t}`,`http://${p}:${g}/${$}/deleteById/${t}`,`http://${p}:${g}/api/${$}/deleteById/${t}`,`/${$}/${t}`,`/api/${$}/${t}`,`/${$}/deleteById/${t}`,`/api/${$}/deleteById/${t}`]:h};export{o as getServiceEndpoints};
