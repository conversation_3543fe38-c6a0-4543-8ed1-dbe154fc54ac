@echo off
echo ========================================
echo DIAGNOSING NETWORK ACCESS ISSUE
echo ========================================
echo.

echo Problem: Projects not fetching data from another PC
echo Testing all possible causes...
echo.

echo ========================================
echo Step 1: Test Backend API Directly
echo ========================================
echo.

echo Testing backend API from this PC...
powershell -Command "try { $response = Invoke-RestMethod -Uri 'http://************:8091/projects' -Method Get; Write-Host 'SUCCESS: Backend API working'; Write-Host 'Projects found:' $response.Length; Write-Host 'Sample project name:' $response[0].name } catch { Write-Host 'ERROR: Backend API failed -' $_.Exception.Message }"

echo.
echo ========================================
echo Step 2: Test CORS Headers
echo ========================================
echo.

echo Testing CORS configuration...
powershell -Command "try { $headers = @{'Origin'='http://************:3060'}; $response = Invoke-WebRequest -Uri 'http://************:8091/projects' -Method Get -Headers $headers; Write-Host 'CORS Headers:'; $response.Headers['Access-Control-Allow-Origin']; Write-Host 'Status:' $response.StatusCode } catch { Write-Host 'CORS Error:' $_.Exception.Message }"

echo.
echo ========================================
echo Step 3: Check Network Connectivity
echo ========================================
echo.

echo Testing network connectivity...
echo Testing ping to ************...
ping -n 2 ************

echo.
echo Testing port 8091 (backend)...
powershell -Command "try { $tcp = New-Object System.Net.Sockets.TcpClient; $tcp.Connect('************', 8091); $tcp.Close(); Write-Host 'Port 8091: OPEN' } catch { Write-Host 'Port 8091: CLOSED or BLOCKED' }"

echo.
echo Testing port 3060 (frontend)...
powershell -Command "try { $tcp = New-Object System.Net.Sockets.TcpClient; $tcp.Connect('************', 3060); $tcp.Close(); Write-Host 'Port 3060: OPEN' } catch { Write-Host 'Port 3060: CLOSED or BLOCKED' }"

echo.
echo ========================================
echo Step 4: Check Windows Firewall
echo ========================================
echo.

echo Checking Windows Firewall rules...
powershell -Command "Get-NetFirewallRule -DisplayName '*Java*' -Enabled True | Select-Object DisplayName, Direction, Action"
powershell -Command "Get-NetFirewallRule -DisplayName '*Node*' -Enabled True | Select-Object DisplayName, Direction, Action"

echo.
echo ========================================
echo Step 5: Test Frontend Accessibility
echo ========================================
echo.

echo Testing if frontend is accessible from network...
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://************:3060' -Method Get -TimeoutSec 10; Write-Host 'Frontend accessible from network IP'; Write-Host 'Status:' $response.StatusCode } catch { Write-Host 'Frontend NOT accessible from network IP:' $_.Exception.Message }"

echo.
echo ========================================
echo Step 6: Create Network Test Page
echo ========================================
echo.

echo Creating a simple network test page...

:: Create a simple HTML test page
echo ^<!DOCTYPE html^> > network-test.html
echo ^<html^> >> network-test.html
echo ^<head^> >> network-test.html
echo     ^<title^>Network API Test^</title^> >> network-test.html
echo ^</head^> >> network-test.html
echo ^<body^> >> network-test.html
echo     ^<h1^>Network API Test^</h1^> >> network-test.html
echo     ^<button onclick="testAPI()"^>Test Projects API^</button^> >> network-test.html
echo     ^<div id="result"^>^</div^> >> network-test.html
echo     ^<script^> >> network-test.html
echo         function testAPI() { >> network-test.html
echo             const result = document.getElementById('result'); >> network-test.html
echo             result.innerHTML = 'Testing...'; >> network-test.html
echo             fetch('http://************:8091/projects') >> network-test.html
echo                 .then(response =^> { >> network-test.html
echo                     if (response.ok) { >> network-test.html
echo                         return response.json(); >> network-test.html
echo                     } else { >> network-test.html
echo                         throw new Error('HTTP ' + response.status); >> network-test.html
echo                     } >> network-test.html
echo                 }) >> network-test.html
echo                 .then(data =^> { >> network-test.html
echo                     result.innerHTML = '^<h3^>SUCCESS!^</h3^>^<pre^>' + JSON.stringify(data, null, 2) + '^</pre^>'; >> network-test.html
echo                 }) >> network-test.html
echo                 .catch(error =^> { >> network-test.html
echo                     result.innerHTML = '^<h3^>ERROR: ' + error.message + '^</h3^>'; >> network-test.html
echo                 }); >> network-test.html
echo         } >> network-test.html
echo     ^</script^> >> network-test.html
echo ^</body^> >> network-test.html
echo ^</html^> >> network-test.html

echo Created network-test.html
echo.

echo ========================================
echo Step 7: Restart Services with Network Config
echo ========================================
echo.

echo Stopping existing services...
taskkill /f /im java.exe 2>nul
taskkill /f /im node.exe 2>nul
timeout /t 3 /nobreak >nul

echo Starting backend with network access...
cd backend
start "Backend - Network Access" cmd /k "echo Starting backend for network access... && mvn spring-boot:run"
cd ..

echo Waiting for backend to start...
timeout /t 15 /nobreak >nul

echo Starting frontend with network access...
cd frontend
start "Frontend - Network Access" cmd /k "echo Starting frontend for network access... && npm run dev -- --host 0.0.0.0"
cd ..

echo Waiting for frontend to start...
timeout /t 10 /nobreak >nul

echo.
echo ========================================
echo DIAGNOSTIC RESULTS AND SOLUTIONS
echo ========================================
echo.

echo Test the following from ANOTHER PC:
echo.

echo 1. Direct API Test:
echo    Open browser and go to: http://************:8091/projects
echo    Expected: JSON data with project information
echo.

echo 2. Network Test Page:
echo    Copy network-test.html to another PC
echo    Open in browser and click "Test Projects API"
echo    Expected: SUCCESS with project data
echo.

echo 3. Frontend Test:
echo    Open browser and go to: http://************:3060/simple-projects
echo    Expected: Projects table with data
echo.

echo ========================================
echo COMMON ISSUES AND FIXES
echo ========================================
echo.

echo If API test fails from another PC:
echo   - Check Windows Firewall (allow Java through firewall)
echo   - Check router/network settings
echo   - Verify IP address is correct: ipconfig
echo.

echo If API works but frontend doesn't:
echo   - Check if frontend is accessible: http://************:3060
echo   - Check browser console for CORS errors
echo   - Verify frontend is running with --host 0.0.0.0
echo.

echo If CORS errors appear:
echo   - Backend CORS should be: @CrossOrigin(origins = "*")
echo   - Check backend logs for CORS configuration
echo.

echo ========================================
echo MANUAL FIXES
echo ========================================
echo.

echo If automatic fixes don't work:
echo.

echo 1. Restart backend with CORS fix:
echo    cd backend
echo    mvn clean compile
echo    mvn spring-boot:run
echo.

echo 2. Restart frontend for network:
echo    cd frontend  
echo    npm run dev -- --host 0.0.0.0 --port 3060
echo.

echo 3. Test from another PC:
echo    http://************:3060/simple-projects
echo.

echo ========================================
echo.

echo Diagnostic complete. Check the results above.
echo Test the URLs from another PC to verify network access.
echo.

pause
