@echo off
echo ========================================
echo STARTING FRONTEND FOR NETWORK ACCESS
echo ========================================
echo.

echo This will start the frontend so it can be accessed from other PCs
echo on the network using IP address ************:3060
echo.

:: Check if we're in the right directory
if not exist "frontend" (
    echo Error: frontend directory not found!
    echo Please run this script from the InvoiceManager directory.
    pause
    exit /b 1
)

:: Stop any existing frontend processes
echo Stopping existing frontend processes...
taskkill /f /im node.exe 2>nul
timeout /t 2 /nobreak >nul

:: Navigate to frontend directory
cd frontend

:: Check if node_modules exists
if not exist "node_modules" (
    echo Installing dependencies...
    call npm install
    if %ERRORLEVEL% neq 0 (
        echo Error: Failed to install dependencies!
        pause
        exit /b 1
    )
)

echo.
echo ========================================
echo Starting Frontend with Network Access
echo ========================================
echo.

echo Configuration:
echo   - Host: 0.0.0.0 (accessible from all network interfaces)
echo   - Port: 3060
echo   - API Backend: http://************:8091
echo.

echo The frontend will be accessible from:
echo   - Same PC: http://localhost:3060
echo   - Other PCs: http://************:3060
echo.

echo Starting frontend server...
echo.

:: Start the frontend with network access
start "Invoice Manager Frontend - Network Access" cmd /k "echo Frontend starting for network access... && npm run dev -- --host 0.0.0.0 --port 3060"

echo.
echo Waiting for frontend to start...
timeout /t 10 /nobreak >nul

echo.
echo ========================================
echo Testing Frontend Accessibility
echo ========================================
echo.

echo Testing localhost access...
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:3060' -Method Get -TimeoutSec 5; Write-Host 'SUCCESS: Frontend accessible on localhost:3060' } catch { Write-Host 'WAITING: Frontend still starting on localhost:3060' }"

echo.
echo Testing network IP access...
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://************:3060' -Method Get -TimeoutSec 5; Write-Host 'SUCCESS: Frontend accessible on network IP ************:3060' } catch { Write-Host 'WAITING: Frontend still starting on network IP' }"

echo.
echo ========================================
echo NETWORK ACCESS TEST URLS
echo ========================================
echo.

echo From the SAME PC:
echo   http://localhost:3060/simple-projects
echo   http://localhost:3060/test-projects
echo.

echo From OTHER PCs on the network:
echo   http://************:3060/simple-projects
echo   http://************:3060/test-projects
echo.

echo ========================================
echo TROUBLESHOOTING
echo ========================================
echo.

echo If you can't access from other PCs:
echo.

echo 1. Check Windows Firewall:
echo    - Go to Windows Defender Firewall
echo    - Click "Allow an app or feature through Windows Defender Firewall"
echo    - Find "Node.js" and check both Private and Public
echo    - If not found, click "Allow another app" and add Node.js
echo.

echo 2. Check Network Settings:
echo    - Ensure all PCs are on the same network
echo    - Test ping from other PC: ping ************
echo    - Check if port 3060 is open: telnet ************ 3060
echo.

echo 3. Check Router Settings:
echo    - Some routers block inter-device communication
echo    - Look for "AP Isolation" or "Client Isolation" and disable it
echo    - Check if "Guest Network" is being used (usually isolated)
echo.

echo ========================================
echo EXPECTED RESULTS
echo ========================================
echo.

echo When working correctly:
echo.

echo ✅ From same PC (localhost:3060):
echo   - Simple Projects page loads
echo   - Shows project data in table
echo   - No errors in browser console
echo.

echo ✅ From other PCs (************:3060):
echo   - Same Simple Projects page loads
echo   - Shows same project data
echo   - No "Failed to fetch" errors
echo   - No CORS errors in browser console
echo.

echo ✅ Browser Console (F12) should show:
echo   - GET http://************:8091/projects - Status 200
echo   - No CORS errors
echo   - Project data logged successfully
echo.

echo ========================================
echo NEXT STEPS
echo ========================================
echo.

echo 1. Wait for frontend to fully start (may take 30-60 seconds)
echo.

echo 2. Test from this PC first:
echo    http://localhost:3060/simple-projects
echo.

echo 3. If working, test from another PC:
echo    http://************:3060/simple-projects
echo.

echo 4. If still not working, run the diagnostic:
echo    .\diagnose-network-issue.bat
echo.

echo ========================================
echo.

cd ..

echo Frontend is starting with network access enabled.
echo Check the URLs above from other PCs to test network access.
echo.

pause
