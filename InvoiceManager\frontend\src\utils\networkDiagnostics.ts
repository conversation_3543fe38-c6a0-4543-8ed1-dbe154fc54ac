// Network Diagnostics Utility
// This utility helps diagnose network configuration issues

export const networkDiagnostics = {
  /**
   * Check environment variables
   */
  checkEnvironment: () => {
    console.log('=== ENVIRONMENT DIAGNOSTICS ===');
    console.log('VITE_API_URL:', import.meta.env.VITE_API_URL);
    console.log('VITE_API_BASE_URL:', import.meta.env.VITE_API_BASE_URL);
    console.log('Current hostname:', window.location.hostname);
    console.log('Current origin:', window.location.origin);
    console.log('================================');
  },

  /**
   * Test API connectivity
   */
  testConnectivity: async () => {
    const serverUrl = import.meta.env.VITE_API_BASE_URL || 'http://************:8091';
    
    console.log('=== CONNECTIVITY TEST ===');
    console.log('Testing server:', serverUrl);
    
    const endpoints = [
      `${serverUrl}/api/spocs`,
      `${serverUrl}/api/projects`,
      `${serverUrl}/api/invoices`,
      `${serverUrl}/api/payments/getAll`,
      `${serverUrl}/redberyl-accounts/getAll`
    ];
    
    for (const endpoint of endpoints) {
      try {
        console.log(`Testing: ${endpoint}`);
        const response = await fetch(endpoint, {
          method: 'GET',
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json'
          }
        });
        
        if (response.ok) {
          const data = await response.json();
          console.log(`✅ SUCCESS: ${Array.isArray(data) ? data.length : 'N/A'} items`);
        } else {
          console.log(`❌ FAILED: ${response.status} ${response.statusText}`);
        }
      } catch (error) {
        console.log(`❌ ERROR: ${error.message}`);
      }
    }
    console.log('========================');
  },

  /**
   * Check CORS headers
   */
  checkCORS: async () => {
    const serverUrl = import.meta.env.VITE_API_BASE_URL || 'http://************:8091';
    
    console.log('=== CORS DIAGNOSTICS ===');
    
    try {
      const response = await fetch(`${serverUrl}/api/spocs`, {
        method: 'OPTIONS'
      });
      
      console.log('CORS Headers:');
      console.log('Access-Control-Allow-Origin:', response.headers.get('Access-Control-Allow-Origin'));
      console.log('Access-Control-Allow-Methods:', response.headers.get('Access-Control-Allow-Methods'));
      console.log('Access-Control-Allow-Headers:', response.headers.get('Access-Control-Allow-Headers'));
      console.log('Access-Control-Allow-Credentials:', response.headers.get('Access-Control-Allow-Credentials'));
    } catch (error) {
      console.log('CORS test failed:', error.message);
    }
    console.log('=======================');
  },

  /**
   * Run all diagnostics
   */
  runAll: async () => {
    console.clear();
    console.log('🔍 RUNNING NETWORK DIAGNOSTICS...\n');
    
    networkDiagnostics.checkEnvironment();
    console.log('');
    
    await networkDiagnostics.checkCORS();
    console.log('');
    
    await networkDiagnostics.testConnectivity();
    
    console.log('\n✅ Diagnostics complete!');
    console.log('If issues persist:');
    console.log('1. Restart frontend: npm run dev');
    console.log('2. Restart backend: mvn spring-boot:run');
    console.log('3. Check firewall settings');
    console.log('4. Verify network connectivity');
  }
};

// Make it available globally for easy access in browser console
if (typeof window !== 'undefined') {
  (window as any).networkDiagnostics = networkDiagnostics;
}
