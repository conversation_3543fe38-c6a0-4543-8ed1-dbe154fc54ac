# 🚀 Server Deployment IP Configuration Guide

## 🎯 **The Problem**
When you push code to Git and deploy on a server, the hardcoded IP `************` won't work because:
- Server will have a different IP address
- Production environment needs different configuration
- You don't want to change code for every deployment

## ✅ **Solution: Environment-Based Configuration**

### **Current Code Structure (Good!)**
Your code already uses environment variables with fallbacks:

```javascript
// This is GOOD - it's flexible!
const serverUrl = import.meta.env.VITE_API_BASE_URL || 'http://************:8091';
```

### **How It Works:**
1. **Development**: Uses `************:8091` (your current setup)
2. **Production**: Uses environment variable from server
3. **Fallback**: If no environment variable, uses the default

## 🔧 **Deployment Options**

### **Option 1: Environment Variables (Recommended)**

#### **On Your Server, Create `.env` File:**
```bash
# Production server .env file
VITE_API_BASE_URL=http://YOUR_SERVER_IP:8091

# Examples:
# VITE_API_BASE_URL=http://************:8091    # Public IP
# VITE_API_BASE_URL=http://**********:8091      # Private IP
# VITE_API_BASE_URL=http://myserver.com:8091    # Domain name
# VITE_API_BASE_URL=https://api.mycompany.com   # HTTPS with domain
```

#### **Deployment Steps:**
```bash
# 1. Clone your repository on server
git clone https://github.com/yourusername/InvoiceManager.git
cd InvoiceManager/frontend

# 2. Create .env file with server's IP
echo "VITE_API_BASE_URL=http://YOUR_SERVER_IP:8091" > .env

# 3. Install and build
npm install
npm run build

# 4. Start the application
npm run preview --host 0.0.0.0 --port 3060
```

### **Option 2: Automatic IP Detection**

#### **Use the Setup Script:**
```bash
# On your server, run:
cd InvoiceManager/frontend
node setup-env.js

# This will automatically detect the server's IP and create .env file
```

#### **For Custom IP:**
```bash
# If you know the server IP:
node setup-env.js --ip=************

# For domain name:
node setup-env.js --ip=myserver.com
```

### **Option 3: Dynamic Hostname Detection**

#### **Leave Environment Empty for Auto-Detection:**
```bash
# In .env file on server:
VITE_API_BASE_URL=

# This will make the app use the current hostname automatically
```

## 🌐 **Different Deployment Scenarios**

### **Scenario 1: Same Server (Backend + Frontend)**
```bash
# .env file:
VITE_API_BASE_URL=http://localhost:8091
```

### **Scenario 2: Different Servers**
```bash
# Frontend server .env file:
VITE_API_BASE_URL=http://BACKEND_SERVER_IP:8091
```

### **Scenario 3: Cloud Deployment**
```bash
# .env file:
VITE_API_BASE_URL=https://api.yourcompany.com
```

### **Scenario 4: Docker Deployment**
```bash
# .env file:
VITE_API_BASE_URL=http://backend-container:8091
```

## 📋 **Step-by-Step Server Deployment**

### **Step 1: Prepare Your Code**
```bash
# Make sure your code uses environment variables (already done!)
# Push to Git
git add .
git commit -m "Fixed IP configuration for deployment"
git push origin main
```

### **Step 2: On Your Server**
```bash
# Clone the repository
git clone https://github.com/yourusername/InvoiceManager.git
cd InvoiceManager

# Configure IP for this server
cd frontend
node setup-env.js  # Auto-detect IP
# OR
node setup-env.js --ip=YOUR_SERVER_IP
```

### **Step 3: Start Backend**
```bash
cd ../backend
mvn spring-boot:run
```

### **Step 4: Start Frontend**
```bash
cd ../frontend
npm install
npm run build
npm run preview --host 0.0.0.0 --port 3060
```

### **Step 5: Access Application**
```
http://YOUR_SERVER_IP:3060
```

## 🔒 **Production Best Practices**

### **1. Use HTTPS in Production**
```bash
# .env file for production:
VITE_API_BASE_URL=https://api.yourcompany.com
```

### **2. Use Domain Names Instead of IPs**
```bash
# Better than IP addresses:
VITE_API_BASE_URL=https://backend.yourcompany.com
```

### **3. Environment-Specific Configuration**
```bash
# Development
VITE_API_BASE_URL=http://************:8091

# Staging
VITE_API_BASE_URL=https://staging-api.yourcompany.com

# Production
VITE_API_BASE_URL=https://api.yourcompany.com
```

## 🛠️ **Quick Commands for Different Servers**

### **Local Development:**
```bash
node setup-env.js --localhost
```

### **Local Network:**
```bash
node setup-env.js --ip=************
```

### **Production Server:**
```bash
node setup-env.js --ip=************
```

### **Domain-Based:**
```bash
node setup-env.js --ip=api.mycompany.com
```

## 📝 **Git Workflow**

### **What to Commit:**
✅ **DO commit:**
- Source code with environment variable usage
- `.env.example` file with examples
- `setup-env.js` script

❌ **DON'T commit:**
- `.env` file (add to `.gitignore`)
- Hardcoded IP addresses in source code

### **Sample .gitignore:**
```
# Environment files
.env
.env.local
.env.production

# But keep the example
!.env.example
```

## 🎉 **Summary**

Your code is already properly configured! When deploying to a server:

1. **Clone your repository**
2. **Run `node setup-env.js --ip=SERVER_IP`**
3. **Start backend and frontend**
4. **Access via server IP**

The environment variable system ensures your code works on any server without code changes! 🚀

## 🔍 **Troubleshooting**

### **If API calls fail on server:**
1. Check `.env` file has correct server IP
2. Verify backend is running on port 8091
3. Check firewall allows port 8091
4. Verify CORS configuration includes server IP

### **Quick diagnostic:**
```bash
# Check current configuration
cat .env

# Test backend connectivity
curl http://YOUR_SERVER_IP:8091/api/health
```
