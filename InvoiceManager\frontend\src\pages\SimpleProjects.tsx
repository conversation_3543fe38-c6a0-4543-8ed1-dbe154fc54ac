import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Loader2, Plus, RefreshCcw } from "lucide-react";

interface Project {
  id: number;
  name: string;
  clientId: number;
  client?: {
    id: number;
    name: string;
  };
  email?: string;
  phone?: string;
  gstNumber?: string;
  state?: string;
  value?: number;
  status?: string;
  created_at?: string;
  updated_at?: string;
}

const SimpleProjects = () => {
  const [projects, setProjects] = useState<Project[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchProjects = async () => {
    setLoading(true);
    setError(null);

    try {
      console.log("SimpleProjects: Fetching projects...");
      
      // Always use direct API call for network access
      // Don't use proxy when accessing from other PCs
      const serverUrl = import.meta.env.VITE_API_BASE_URL || 'http://192.168.1.30:8091';

      // Use the working endpoint with authentication (same as retry logic in Clients.tsx)
      const endpoints = [
        `${serverUrl}/projects/getAll`, // Working endpoint with auth (primary)
        `${serverUrl}/test/projects`,   // Simple test endpoint (fallback)
        `${serverUrl}/projects`,        // Original endpoint (fallback)
      ];

      console.log("SimpleProjects: Trying endpoints:", endpoints);
      console.log("SimpleProjects: Current window location:", window.location.href);

      // Try endpoints in order until one works
      let lastError = null;
      let data = null;

      for (const endpoint of endpoints) {
        try {
          console.log("SimpleProjects: Trying endpoint:", endpoint);

          // Use authentication for /projects/getAll endpoint
          const headers: Record<string, string> = {
            'Accept': 'application/json',
            'Content-Type': 'application/json'
          };

          // Add auth for the working endpoint
          if (endpoint.includes('/projects/getAll')) {
            headers['Authorization'] = 'Basic ' + btoa('admin:admin123');
          }

          const response = await fetch(endpoint, {
            method: 'GET',
            headers,
            credentials: endpoint.includes('/projects/getAll') ? 'omit' : 'include'
          });

          console.log("SimpleProjects: Response status:", response.status);

          if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
          }

          data = await response.json();
          console.log("SimpleProjects: SUCCESS with endpoint:", endpoint);
          console.log("SimpleProjects: API response:", data);
          break; // Success, exit loop

        } catch (error) {
          console.log("SimpleProjects: Failed endpoint:", endpoint, "Error:", error.message);
          lastError = error;
          continue; // Try next endpoint
        }
      }

      // If all endpoints failed, throw the last error
      if (!data) {
        throw lastError || new Error("All endpoints failed");
      }

      // Handle both single project and array responses
      const projectsArray = Array.isArray(data) ? data : [data];
      setProjects(projectsArray);

      console.log("SimpleProjects: Set projects:", projectsArray.length);

    } catch (err) {
      console.error("SimpleProjects: Error:", err);
      setError(err instanceof Error ? err.message : String(err));
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchProjects();
  }, []);

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Projects</h1>
        <div className="flex space-x-2">
          <Button onClick={fetchProjects} disabled={loading} variant="outline">
            {loading ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <RefreshCcw className="mr-2 h-4 w-4" />}
            Refresh
          </Button>
          <Button>
            <Plus className="mr-2 h-4 w-4" />
            Add Project
          </Button>
        </div>
      </div>

      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardHeader>
            <CardTitle className="text-red-800">Error Loading Projects</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-red-700">{error}</p>
            <Button onClick={fetchProjects} className="mt-2" variant="outline">
              Try Again
            </Button>
          </CardContent>
        </Card>
      )}

      <Card>
        <CardHeader>
          <CardTitle>Projects List ({projects.length} projects)</CardTitle>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex items-center justify-center p-8">
              <Loader2 className="h-8 w-8 animate-spin" />
              <span className="ml-2">Loading projects...</span>
            </div>
          ) : projects.length > 0 ? (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Client</TableHead>
                  <TableHead>Email</TableHead>
                  <TableHead>Phone</TableHead>
                  <TableHead>State</TableHead>
                  <TableHead>Value</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {projects.map((project) => (
                  <TableRow key={project.id}>
                    <TableCell className="font-medium">{project.name}</TableCell>
                    <TableCell>{project.client?.name || `Client ${project.clientId}`}</TableCell>
                    <TableCell>{project.email || '-'}</TableCell>
                    <TableCell>{project.phone || '-'}</TableCell>
                    <TableCell>{project.state || '-'}</TableCell>
                    <TableCell>{project.value ? `$${project.value}` : '-'}</TableCell>
                    <TableCell>
                      <span className="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs">
                        {project.status || 'Active'}
                      </span>
                    </TableCell>
                    <TableCell>
                      <div className="flex space-x-2">
                        <Button size="sm" variant="outline">Edit</Button>
                        <Button size="sm" variant="outline">View</Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          ) : (
            <div className="text-center p-8">
              <p className="text-gray-500 mb-4">No projects found</p>
              <Button onClick={fetchProjects} variant="outline">
                <RefreshCcw className="mr-2 h-4 w-4" />
                Refresh
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {projects.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Project Details</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {projects.map((project) => (
                <div key={project.id} className="border rounded p-4 bg-gray-50">
                  <h3 className="font-semibold text-lg mb-2">{project.name}</h3>
                  <div className="space-y-1 text-sm">
                    <div><strong>ID:</strong> {project.id}</div>
                    <div><strong>Client:</strong> {project.client?.name || `Client ${project.clientId}`}</div>
                    <div><strong>Email:</strong> {project.email || 'Not provided'}</div>
                    <div><strong>Phone:</strong> {project.phone || 'Not provided'}</div>
                    <div><strong>GST Number:</strong> {project.gstNumber || 'Not provided'}</div>
                    <div><strong>State:</strong> {project.state || 'Not provided'}</div>
                    <div><strong>Created:</strong> {project.created_at ? new Date(project.created_at).toLocaleDateString() : 'Unknown'}</div>
                    <div><strong>Updated:</strong> {project.updated_at ? new Date(project.updated_at).toLocaleDateString() : 'Unknown'}</div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      <Card>
        <CardHeader>
          <CardTitle>API Status</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <span className={`w-3 h-3 rounded-full ${!error ? 'bg-green-500' : 'bg-red-500'}`}></span>
              <span>API: {!error ? 'Connected' : 'Error'}</span>
            </div>
            <div className="flex items-center space-x-2">
              <span className={`w-3 h-3 rounded-full ${projects.length > 0 ? 'bg-green-500' : 'bg-yellow-500'}`}></span>
              <span>Data: {projects.length > 0 ? `${projects.length} projects` : 'No data'}</span>
            </div>
            <div className="text-sm text-gray-500">
              Endpoint: http://192.168.1.30:8091/projects
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default SimpleProjects;
