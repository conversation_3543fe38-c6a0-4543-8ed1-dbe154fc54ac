@echo off
echo ========================================
echo Testing Payments Network Connectivity
echo ========================================
echo.

echo Testing backend connectivity...

echo.
echo ===== Testing Backend Connection =====
curl -s http://************:8091/api/payments/getAll
if %errorlevel% equ 0 (
    echo ✅ Backend is running and accessible!
) else (
    echo ❌ Backend is not running or not accessible.
    echo Please start the backend first:
    echo   cd backend
    echo   mvn spring-boot:run
    pause
    exit /b 1
)

echo.
echo ===== Testing Payment Endpoints =====

echo.
echo --- Testing GET /api/payments/getAll ---
curl -s -H "Authorization: Basic YWRtaW46YWRtaW4xMjM=" http://************:8091/api/payments/getAll

echo.
echo --- Testing GET /payments ---
curl -s -H "Authorization: Basic YWRtaW46YWRtaW4xMjM=" http://************:8091/payments

echo.
echo ===== Testing Payment Creation =====
curl -X POST http://************:8091/api/payments/create ^
  -H "Content-Type: application/json" ^
  -H "Authorization: Basic YWRtaW46YWRtaW4xMjM=" ^
  -d "{\"invoiceId\":1,\"amountReceived\":5000,\"receivedOn\":\"2024-01-15\",\"paymentMode\":\"Bank Transfer\",\"referenceNumber\":\"TEST-001\"}"

echo.
echo ===== Testing Other Fixed Endpoints =====

echo.
echo --- Testing Projects ---
curl -s -H "Authorization: Basic YWRtaW46YWRtaW4xMjM=" http://************:8091/projects

echo.
echo --- Testing Staffing Types ---
curl -s -H "Authorization: Basic YWRtaW46YWRtaW4xMjM=" http://************:8091/staffing-types/getAll

echo.
echo --- Testing HSN Codes ---
curl -s -H "Authorization: Basic YWRtaW46YWRtaW4xMjM=" http://************:8091/api/hsn-codes

echo.
echo ===== Network Test Complete =====
echo All endpoints tested. Check the responses above.
echo.
echo Frontend is available at: http://************:3060/payments
echo.
echo From another PC, you should be able to:
echo 1. Open: http://************:3060/payments
echo 2. See payment data loading without errors
echo 3. No more localhost:8091 errors in browser console
echo.
pause
