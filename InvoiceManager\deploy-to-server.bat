@echo off
echo ========================================
echo Server Deployment Configuration Script
echo ========================================
echo.

echo This script helps configure the application for server deployment.
echo.

set /p SERVER_IP="Enter your server IP address (e.g., ************): "

if "%SERVER_IP%"=="" (
    echo Error: Server IP is required!
    pause
    exit /b 1
)

echo.
echo Configuring application for server IP: %SERVER_IP%
echo.

echo ========================================
echo Step 1: Configure Frontend Environment
echo ========================================
echo.

cd frontend

echo Creating .env file for server deployment...
echo # Server deployment configuration > .env
echo # Generated on: %date% %time% >> .env
echo VITE_API_BASE_URL=http://%SERVER_IP%:8091 >> .env
echo VITE_API_URL=http://%SERVER_IP%:8091/api >> .env

echo ✅ Frontend environment configured
echo.

echo Current .env configuration:
type .env
echo.

echo ========================================
echo Step 2: Backend CORS Configuration
echo ========================================
echo.

cd ..\backend\src\main\java\com\redberyl\invoiceapp\config

echo Checking CORS configuration...
findstr /C:"%SERVER_IP%" CorsConfig.java >nul
if %ERRORLEVEL% equ 0 (
    echo ✅ Server IP already configured in CORS
) else (
    echo ⚠️  Server IP not found in CORS configuration
    echo You may need to add %SERVER_IP%:3060 to CorsConfig.java
)

cd ..\..\..\..\..

echo ========================================
echo Step 3: Deployment Instructions
echo ========================================
echo.

echo To deploy on your server:
echo.
echo 1. Copy this entire InvoiceManager folder to your server
echo.
echo 2. On the server, start the backend:
echo    cd backend
echo    mvn spring-boot:run
echo.
echo 3. On the server, start the frontend:
echo    cd frontend
echo    npm install
echo    npm run build
echo    npm run preview --host 0.0.0.0 --port 3060
echo.
echo 4. Access the application:
echo    http://%SERVER_IP%:3060
echo.

echo ========================================
echo Step 4: Test Configuration
echo ========================================
echo.

echo Testing backend connectivity to %SERVER_IP%...
ping -n 1 %SERVER_IP% >nul
if %ERRORLEVEL% equ 0 (
    echo ✅ Server %SERVER_IP% is reachable
) else (
    echo ❌ Server %SERVER_IP% is not reachable
    echo Please check the IP address and network connectivity
)

echo.
echo ========================================
echo Configuration Complete!
echo ========================================
echo.

echo Your application is now configured for server: %SERVER_IP%
echo.
echo Next steps:
echo 1. Push your code to Git (the .env file will be ignored)
echo 2. Clone on your server
echo 3. Run this script again on the server with the server's IP
echo 4. Start backend and frontend services
echo.

echo Git commands:
echo   git add .
echo   git commit -m "Configured for server deployment"
echo   git push origin main
echo.

pause
