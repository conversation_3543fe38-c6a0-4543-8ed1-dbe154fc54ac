import { api } from './api';
import { getProxiedUrl, getProxiedUrls, getBasicAuthHeader } from '@/utils/apiUtils';

// No mock data - use real API only

export interface Client {
  id: number;
  name: string;
  // Backend response fields
  created_at?: string;
  updated_at?: string;

  // Frontend fields (may not be in the response)
  email?: string;
  phone?: string;
  contactPerson?: string;
  website?: string;
  bdmId?: number;
  bdmName?: string; // This is for display only, not sent in requests
  commissionPercentage?: string | number;
  billingAddress?: string;
  shippingAddress?: string;
  gstNumber?: string;
  panNumber?: string;
  cinNumber?: string;
  notes?: string;

  // Mapped fields for consistency
  createdAt?: string;
  updatedAt?: string;
}

/**
 * Maps the backend client response to the frontend client format
 * @param client The client data from the backend
 * @returns Client with consistent field names
 */
export const mapClientResponse = (client: any): Client => {
  if (!client) return {} as Client;

  return {
    ...client,
    // Map snake_case to camelCase for consistency
    createdAt: client.created_at || client.createdAt,
    updatedAt: client.updated_at || client.updatedAt,
  };
};

export const clientService = {
  /**
   * Get all clients
   * @returns Promise with array of clients
   */
  getAllClients: async (): Promise<Client[]> => {
    try {
      console.log('ClientService: Fetching all clients');

      // Get server URL from environment
      const serverUrl = import.meta.env.VITE_API_BASE_URL || 'http://************:8091';

      // Use working endpoint pattern (similar to projects/getAll)
      const endpoints = [
        `${serverUrl}/clients/getAll`,  // Try getAll pattern first (with auth)
        `${serverUrl}/clients`,         // Direct to backend controller
        `${serverUrl}/api/clients`,     // Direct to main controller (requires auth)
      ];

      let clientsData = null;
      let success = false;

      // First try using the API service
      try {
        console.log('ClientService: Trying to fetch clients using api.getClients()');
        const response = await api.getClients();
        console.log('ClientService: Raw clients response from api.getClients():', response);

        // Handle different response formats
        if (response) {
          if (Array.isArray(response)) {
            clientsData = response;
            success = true;
          } else if (typeof response === 'object') {
            // Check if it's in ApiResponseDto format
            if ('data' in response && Array.isArray(response.data)) {
              clientsData = response.data;
              success = true;
            } else if ('data' in response && response.data && 'content' in response.data && Array.isArray(response.data.content)) {
              clientsData = response.data.content;
              success = true;
            } else if ('content' in response && Array.isArray(response.content)) {
              clientsData = response.content;
              success = true;
            }
          }
        }
      } catch (apiError) {
        console.error('ClientService: Error fetching clients using api.getClients():', apiError);
      }

      // If API service failed, try direct fetch to multiple endpoints
      if (!success) {
        console.log('ClientService: API service failed, trying direct fetch to multiple endpoints');

        const authHeader = 'Basic ' + btoa('admin:admin123');

        for (const endpoint of endpoints) {
          if (success) break;

          try {
            console.log(`ClientService: Trying to fetch clients from ${endpoint}`);
            const response = await fetch(endpoint, {
              method: 'GET',
              headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json',
                'Authorization': authHeader
              }
              // Removed credentials: 'include' as it can cause issues with CORS
            });

            if (!response.ok) {
              console.error(`ClientService: Endpoint ${endpoint} failed with status: ${response.status}`);
              continue;
            }

            const data = await response.json();
            console.log(`ClientService: Successfully fetched data from ${endpoint}:`, data);

            // Process the data based on its format
            if (Array.isArray(data)) {
              clientsData = data;
              success = true;
            } else if (data && typeof data === 'object') {
              if ('data' in data && Array.isArray(data.data)) {
                clientsData = data.data;
                success = true;
              } else if ('data' in data && data.data && 'content' in data.data && Array.isArray(data.data.content)) {
                clientsData = data.data.content;
                success = true;
              } else if ('content' in data && Array.isArray(data.content)) {
                clientsData = data.content;
                success = true;
              } else if (data.id && data.name) {
                // Single client object
                clientsData = [data];
                success = true;
              }
            }
          } catch (endpointError) {
            console.error(`ClientService: Error fetching from ${endpoint}:`, endpointError);
          }
        }
      }

      // Process and return the data if we have it
      if (success && clientsData && clientsData.length > 0) {
        console.log('ClientService: Clients fetched successfully:', clientsData);
        return clientsData.map((client: any) => mapClientResponse(client));
      }

      // If all attempts failed, throw error instead of using mock data
      throw new Error('Failed to fetch clients from any endpoint');
    } catch (error) {
      console.error('ClientService: Unexpected error in getAllClients:', error);
      throw error;
    }
  },

  /**
   * Get client by ID
   * @param id Client ID
   * @returns Promise with client data
   */
  getClientById: async (id: number): Promise<Client> => {
    try {
      console.log(`ClientService: Fetching client with ID ${id}`);
      const client = await api.getClient(id);

      // Check if client is an object
      if (!client || typeof client !== 'object') {
        console.warn(`ClientService: API did not return a valid client object for ID ${id}`, client);
        throw new Error(`Could not fetch client with ID ${id}`);
      }

      console.log(`ClientService: Successfully fetched client with ID ${id}`, client);
      return client;
    } catch (error) {
      console.error(`ClientService: Error fetching client with ID ${id}:`, error);

      // Try to fetch client directly from the API
      try {
        console.log(`ClientService: Trying alternative approach to fetch client with ID ${id}`);

        // Create basic auth header
        const authHeader = getBasicAuthHeader();

        // Get server URL from environment
        const serverUrl = import.meta.env.VITE_API_BASE_URL || 'http://************:8091';

        // Try multiple endpoints for getting client by ID (prioritize backend controller without auth)
        const endpoints = [
          `${serverUrl}/clients/${id}`,        // Direct to backend controller - primary (no auth)
          `${serverUrl}/clients/public/${id}`, // Direct to public controller (no auth)
          `${serverUrl}/api/clients/${id}`,     // Direct to main controller (requires auth)
          `/clients/${id}`,                             // Proxied backend controller
          `/api/clients/${id}`                          // Proxied main controller
        ];

        for (const endpoint of endpoints) {
          try {
            console.log(`ClientService: Trying endpoint ${endpoint}`);
            const response = await fetch(endpoint, {
              method: 'GET',
              headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json',
                'Authorization': authHeader
              }
              // Remove credentials: 'include' as it conflicts with wildcard CORS
            });

            if (!response.ok) {
              console.warn(`ClientService: Endpoint ${endpoint} returned status ${response.status}`);
              continue;
            }

            const data = await response.json();
            console.log(`ClientService: Successfully fetched client from ${endpoint}`, data);

            // Map and return the client data
            return mapClientResponse(data);
          } catch (endpointError) {
            console.error(`ClientService: Error fetching from ${endpoint}:`, endpointError);
          }
        }

        // If all endpoints fail, throw the original error
        throw error;
      } catch (fallbackError) {
        console.error(`ClientService: All approaches to fetch client with ID ${id} failed:`, fallbackError);
        throw error;
      }
    }
  },

  /**
   * Create a new client
   * @param client Client data
   * @returns Promise with created client data
   */
  createClient: async (client: Partial<Client>): Promise<Client> => {
    try {
      console.log('ClientService: Creating new client', client);

      // Create basic auth header
      const authHeader = getBasicAuthHeader();

      // Get server URL from environment
      const serverUrl = import.meta.env.VITE_API_BASE_URL || 'http://************:8091';

      // Try multiple endpoints for creating client (prioritize backend controller without auth)
      const endpoints = [
        `${serverUrl}/clients`,        // Direct to backend controller - primary (no auth)
        `${serverUrl}/api/clients`,     // Direct to main controller (requires auth)
        '/clients',                             // Proxied backend controller
        '/api/clients'                          // Proxied main controller
      ];

      for (const endpoint of endpoints) {
        try {
          console.log(`ClientService: Trying endpoint ${endpoint}`);
          const response = await fetch(endpoint, {
            method: 'POST',
            headers: {
              'Accept': 'application/json',
              'Content-Type': 'application/json',
              'Authorization': authHeader
            },
            body: JSON.stringify(client)
            // Remove credentials: 'include' as it conflicts with wildcard CORS
          });

          if (!response.ok) {
            console.warn(`ClientService: Endpoint ${endpoint} returned status ${response.status}`);
            continue;
          }

          const data = await response.json();
          console.log(`ClientService: Successfully created client using ${endpoint}`, data);

          // Map and return the created client data
          return mapClientResponse(data);
        } catch (endpointError) {
          console.error(`ClientService: Error creating client using ${endpoint}:`, endpointError);
        }
      }

      throw new Error('Failed to create client');
    } catch (error) {
      console.error('ClientService: Error creating client:', error);
      throw error;
    }
  },

  /**
   * Update an existing client
   * @param id Client ID
   * @param client Client data
   * @returns Promise with updated client data
   */
  updateClient: async (id: number, client: Partial<Client>): Promise<Client> => {
    try {
      console.log(`ClientService: Updating client with ID ${id}`, client);

      // Get server URL from environment
      const serverUrl = import.meta.env.VITE_API_BASE_URL || 'http://************:8091';

      // Try multiple endpoints for updating client (prioritize backend controller without auth)
      const endpoints = [
        `${serverUrl}/clients/${id}`,        // Direct to backend controller - primary (no auth)
        `${serverUrl}/api/clients/${id}`,     // Direct to main controller (requires auth)
        `/clients/${id}`,                             // Proxied backend controller
        `/api/clients/${id}`                          // Proxied main controller
      ];

      for (const endpoint of endpoints) {
        try {
          console.log(`🔄 Trying client update endpoint: ${endpoint}`);

          const response = await fetch(endpoint, {
            method: 'PUT',
            headers: {
              'Accept': 'application/json',
              'Content-Type': 'application/json',
              'Authorization': getBasicAuthHeader()
            },
            body: JSON.stringify(client),
            credentials: 'omit'
          });

          if (response.ok) {
            const data = await response.json();
            console.log(`✅ Successfully updated client via ${endpoint}:`, data);
            return mapClientResponse(data);
          } else {
            console.log(`❌ Failed to update via ${endpoint}: ${response.status}`);
          }
        } catch (error) {
          console.log(`❌ Error with endpoint ${endpoint}:`, error);
        }
      }

      throw new Error(`Failed to update client with ID ${id}`);
    } catch (error) {
      console.error(`ClientService: Error updating client with ID ${id}:`, error);
      throw error;
    }
  },

  /**
   * Delete a client
   * @param id Client ID
   * @returns Promise with void
   */
  deleteClient: async (id: number): Promise<void> => {
    try {
      console.log(`ClientService: Deleting client with ID ${id}`);

      // Get server URL from environment
      const serverUrl = import.meta.env.VITE_API_BASE_URL || 'http://************:8091';

      // Try multiple endpoints for deleting client (prioritize backend controller without auth)
      const endpoints = [
        `${serverUrl}/clients/${id}`,        // Direct to backend controller - primary (no auth)
        `${serverUrl}/api/clients/${id}`,     // Direct to main controller (requires auth)
        `/clients/${id}`,                             // Proxied backend controller
        `/api/clients/${id}`                          // Proxied main controller
      ];

      for (const endpoint of endpoints) {
        try {
          console.log(`🔄 Trying client delete endpoint: ${endpoint}`);

          const response = await fetch(endpoint, {
            method: 'DELETE',
            headers: {
              'Accept': 'application/json',
              'Content-Type': 'application/json',
              'Authorization': getBasicAuthHeader()
            },
            credentials: 'omit'
          });

          if (response.ok || response.status === 204) {
            console.log(`✅ Successfully deleted client via ${endpoint}`);
            return;
          } else {
            console.log(`❌ Failed to delete via ${endpoint}: ${response.status}`);
          }
        } catch (error) {
          console.log(`❌ Error with endpoint ${endpoint}:`, error);
        }
      }

      throw new Error(`Failed to delete client with ID ${id}`);
    } catch (error) {
      console.error(`ClientService: Error deleting client with ID ${id}:`, error);
      throw error;
    }
  }
};

export default clientService;
