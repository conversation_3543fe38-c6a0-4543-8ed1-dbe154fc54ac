package com.redberyl.invoiceapp.controller;

import com.redberyl.invoiceapp.dto.SpocDto;
import com.redberyl.invoiceapp.exception.NoContentException;
import com.redberyl.invoiceapp.service.SpocService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

/**
 * Public controller for SPOCs that doesn't require authentication
 * This is useful for testing and development purposes
 */
@RestController
@RequestMapping("/spocs")
@Tag(name = "Public SPOC API", description = "Public API for SPOCs (no authentication required)")
@CrossOrigin(origins = "*", maxAge = 3600)
public class PublicSpocController {

    @Autowired
    private SpocService spocService;

    @GetMapping
    @Operation(summary = "Get all SPOCs (public)", description = "Get all SPOCs without authentication")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "SPOCs found"),
            @ApiResponse(responseCode = "204", description = "No SPOCs found", content = @Content)
    })
    public ResponseEntity<List<SpocDto>> getAllSpocs() {
        try {
            List<SpocDto> spocs = spocService.getAllSpocs();
            System.out.println("PublicSpocController: Returning " + spocs.size() + " SPOCs");
            return new ResponseEntity<>(spocs, HttpStatus.OK);
        } catch (NoContentException e) {
            System.out.println("PublicSpocController: No SPOCs found");
            return ResponseEntity.noContent().build();
        } catch (Exception e) {
            System.err.println("PublicSpocController: Error fetching SPOCs: " + e.getMessage());
            e.printStackTrace();
            return new ResponseEntity<>(new ArrayList<>(), HttpStatus.OK);
        }
    }

    @GetMapping("/{id}")
    @Operation(summary = "Get SPOC by ID (public)", description = "Get SPOC by ID without authentication")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "SPOC found"),
            @ApiResponse(responseCode = "404", description = "SPOC not found"),
            @ApiResponse(responseCode = "400", description = "Invalid ID supplied")
    })
    public ResponseEntity<SpocDto> getSpocById(@PathVariable Long id) {
        try {
            SpocDto spoc = spocService.getSpocById(id);
            System.out.println("PublicSpocController: Returning SPOC with ID: " + id);
            return new ResponseEntity<>(spoc, HttpStatus.OK);
        } catch (Exception e) {
            System.err.println("PublicSpocController: Error fetching SPOC with ID " + id + ": " + e.getMessage());
            return ResponseEntity.notFound().build();
        }
    }

    @PostMapping
    @Operation(summary = "Create SPOC (public)", description = "Create SPOC without authentication")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "201", description = "SPOC created successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid input")
    })
    public ResponseEntity<SpocDto> createSpoc(@Valid @RequestBody SpocDto spocDto) {
        try {
            SpocDto createdSpoc = spocService.createSpoc(spocDto);
            System.out.println("PublicSpocController: Created SPOC with ID: " + createdSpoc.getId());
            return new ResponseEntity<>(createdSpoc, HttpStatus.CREATED);
        } catch (Exception e) {
            System.err.println("PublicSpocController: Error creating SPOC: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.badRequest().build();
        }
    }

    @PutMapping("/{id}")
    @Operation(summary = "Update SPOC (public)", description = "Update SPOC without authentication")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "SPOC updated successfully"),
            @ApiResponse(responseCode = "404", description = "SPOC not found"),
            @ApiResponse(responseCode = "400", description = "Invalid input")
    })
    public ResponseEntity<SpocDto> updateSpoc(@PathVariable Long id, @Valid @RequestBody SpocDto spocDto) {
        try {
            SpocDto updatedSpoc = spocService.updateSpoc(id, spocDto);
            System.out.println("PublicSpocController: Updated SPOC with ID: " + id);
            return new ResponseEntity<>(updatedSpoc, HttpStatus.OK);
        } catch (Exception e) {
            System.err.println("PublicSpocController: Error updating SPOC with ID " + id + ": " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.badRequest().build();
        }
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "Delete SPOC (public)", description = "Delete SPOC without authentication")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "204", description = "SPOC deleted successfully"),
            @ApiResponse(responseCode = "404", description = "SPOC not found"),
            @ApiResponse(responseCode = "400", description = "Invalid ID supplied")
    })
    public ResponseEntity<Void> deleteSpoc(@PathVariable Long id) {
        try {
            spocService.deleteSpoc(id);
            System.out.println("PublicSpocController: Deleted SPOC with ID: " + id);
            return ResponseEntity.noContent().build();
        } catch (Exception e) {
            System.err.println("PublicSpocController: Error deleting SPOC with ID " + id + ": " + e.getMessage());
            return ResponseEntity.notFound().build();
        }
    }

    @GetMapping("/sample")
    @Operation(summary = "Get sample SPOCs", description = "This endpoint no longer returns sample data")
    public ResponseEntity<List<SpocDto>> getSampleSpocs() {
        System.out.println("Sample SPOCs endpoint called, returning empty list");
        // Return empty list instead of sample data
        return new ResponseEntity<>(new ArrayList<>(), HttpStatus.OK);
    }
}
