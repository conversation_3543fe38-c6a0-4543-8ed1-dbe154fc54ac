-- Migration to add invoice template configuration table
-- This allows dynamic configuration of invoice template values

CREATE TABLE invoice_template_config (
    id BIGSERIAL PRIMARY KEY,
    config_key VARCHAR(255) UNIQUE NOT NULL,
    config_value TEXT,
    config_type VARCHAR(50) DEFAULT 'TEXT',
    category VARCHAR(100),
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    display_order INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX idx_invoice_template_config_key ON invoice_template_config(config_key);
CREATE INDEX idx_invoice_template_config_category ON invoice_template_config(category);
CREATE INDEX idx_invoice_template_config_active ON invoice_template_config(is_active);
CREATE INDEX idx_invoice_template_config_order ON invoice_template_config(display_order);

-- Insert default configuration values
INSERT INTO invoice_template_config (config_key, config_value, config_type, category, description, display_order) VALUES
-- Company Information
('company.name', 'RedBeryl Tech Solutions', 'TEXT', 'COMPANY', 'Company Name', 1),
('company.address', '507-B Amanora Chambers', 'TEXT', 'COMPANY', 'Company Address Line 1', 2),
('company.city', 'Amanora Mall, Hadapsar, Pune 411028', 'TEXT', 'COMPANY', 'Company Address Line 2', 3),
('company.phone', '+91 **********', 'TEXT', 'COMPANY', 'Company Phone Number', 4),
('company.email', '<EMAIL>', 'TEXT', 'COMPANY', 'Company Email', 5),
('company.website', 'www.redberyl.com', 'TEXT', 'COMPANY', 'Company Website', 6),
('company.gstn', '27**********1Z5', 'TEXT', 'COMPANY', 'Company GSTN', 7),
('company.pan', '**********', 'TEXT', 'COMPANY', 'Company PAN', 8),
('company.cin', 'U72200PN2020PTC123456', 'TEXT', 'COMPANY', 'Company CIN', 9),

-- Bank Information
('bank.name', 'HDFC Bank', 'TEXT', 'BANK', 'Bank Name', 10),
('bank.branch', 'Hadapsar Branch', 'TEXT', 'BANK', 'Bank Branch', 11),
('bank.account.name', 'RedBeryl Tech Solutions', 'TEXT', 'BANK', 'Account Holder Name', 12),
('bank.account.number', '**************', 'TEXT', 'BANK', 'Account Number', 13),
('bank.ifsc', 'HDFC0001234', 'TEXT', 'BANK', 'IFSC Code', 14),
('bank.account.type', 'Current Account', 'TEXT', 'BANK', 'Account Type', 15),

-- Template Settings
('template.logo.url', '/images/logo.png', 'TEXT', 'TEMPLATE', 'Logo URL', 16),
('template.footer.text', 'Thank you for your business!', 'TEXT', 'TEMPLATE', 'Footer Text', 17),
('template.terms.conditions', 'Payment due within 30 days', 'TEXT', 'TEMPLATE', 'Terms and Conditions', 18),
('template.currency.symbol', '₹', 'TEXT', 'TEMPLATE', 'Currency Symbol', 19),
('template.date.format', 'MM/dd/yyyy', 'TEXT', 'TEMPLATE', 'Date Format', 20);

-- Add trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_invoice_template_config_updated_at 
    BEFORE UPDATE ON invoice_template_config 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
