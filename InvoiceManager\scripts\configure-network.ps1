# Network Configuration Script for Invoice Manager
param(
    [string]$CustomIP = "",
    [switch]$UseLocalhost = $false,
    [switch]$Help = $false
)

function Write-Success { Write-Host $args -ForegroundColor Green }
function Write-Warning { Write-Host $args -ForegroundColor Yellow }
function Write-Error { Write-Host $args -ForegroundColor Red }
function Write-Info { Write-Host $args -ForegroundColor Cyan }

if ($Help) {
    Write-Info "=== Invoice Manager Network Configuration ==="
    Write-Host ""
    Write-Host "Usage:"
    Write-Host "  .\configure-network.ps1                    # Auto-detect IP and configure"
    Write-Host "  .\configure-network.ps1 -UseLocalhost      # Configure for localhost only"
    Write-Host "  .\configure-network.ps1 -CustomIP *************  # Use specific IP"
    Write-Host "  .\configure-network.ps1 -Help              # Show this help"
    exit 0
}

Write-Info "=== Invoice Manager Network Configuration ==="
Write-Host ""

# Function to get local IP address
function Get-LocalIPAddress {
    try {
        $ipAddress = (Get-NetIPConfiguration | Where-Object { $_.IPv4Address.IPAddress -notlike "169.254.*" -and $_.IPv4Address.IPAddress -ne "127.0.0.1" }).IPv4Address.IPAddress | Select-Object -First 1
        return $ipAddress
    }
    catch {
        Write-Warning "Could not auto-detect IP address. Please specify manually with -CustomIP parameter."
        return $null
    }
}

# Determine IP address to use
if ($UseLocalhost) {
    $targetIP = "localhost"
    Write-Info "Using localhost configuration"
} elseif ($CustomIP) {
    $targetIP = $CustomIP
    Write-Info "Using custom IP: $targetIP"
} else {
    $detectedIP = Get-LocalIPAddress
    if ($detectedIP) {
        $targetIP = $detectedIP
        Write-Success "Detected IP address: $targetIP"
    } else {
        Write-Error "Failed to detect IP address. Please run with -CustomIP parameter."
        exit 1
    }
}

# Configure frontend environment
Write-Info "Configuring frontend environment..."
$frontendPath = Join-Path $PSScriptRoot "..\frontend"
$envPath = Join-Path $frontendPath ".env"

if ($UseLocalhost) {
    $apiUrl = "http://localhost:8091/api"
} else {
    $apiUrl = "http://$targetIP:8091/api"
}

$mode = if ($UseLocalhost) { 'localhost' } else { 'network' }
$timestamp = Get-Date -Format "yyyy-MM-ddTHH:mm:ss.fffZ"

$envContent = "# Auto-generated environment file`n# Backend API URL`nVITE_API_URL=$apiUrl`n`n# Generated on: $timestamp`n# IP Address: $targetIP`n# Mode: $mode"

Set-Content -Path $envPath -Value $envContent -Encoding UTF8
Write-Success "✅ Frontend environment configured: $envPath"
Write-Info "🌐 API URL: $apiUrl"

Write-Host ""
Write-Success "=== Configuration Complete ==="
Write-Host ""

# Display access information
Write-Info "🚀 How to start your application:"
Write-Host ""
Write-Host "1. Start Backend (in a new terminal):"
Write-Info "   cd backend && mvn spring-boot:run"
Write-Host ""
Write-Host "2. Start Frontend (in another terminal):"
Write-Info "   cd frontend && npm run dev"
Write-Host ""

Write-Info "🌐 Access URLs:"
if ($UseLocalhost) {
    Write-Success "   Local: http://localhost:3060"
} else {
    Write-Success "   Local: http://localhost:3060"
    Write-Success "   Network: http://$targetIP:3060"
    Write-Host ""
    Write-Info "📱 From other devices on your network:"
    Write-Success "   http://$targetIP:3060"
}

Write-Host ""
Write-Warning "🔥 Firewall Configuration:"
Write-Host "Make sure Windows Firewall allows connections on ports:"
Write-Host "  - Port 3060 (Frontend)"
Write-Host "  - Port 8091 (Backend)"
Write-Host ""
Write-Info "To configure firewall automatically, run as Administrator:"
Write-Info "   netsh advfirewall firewall add rule name=`"Invoice App Frontend`" dir=in action=allow protocol=TCP localport=3060"
Write-Info "   netsh advfirewall firewall add rule name=`"Invoice App Backend`" dir=in action=allow protocol=TCP localport=8091"
