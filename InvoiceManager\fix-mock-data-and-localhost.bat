@echo off
echo ========================================
echo FIXING MOCK DATA AND LOCALHOST ISSUES
echo ========================================
echo.

echo Problem: Mock data showing and localhost connection errors from another PC
echo Solution: Remove all mock data and fix all localhost URLs
echo.

echo ========================================
echo Step 1: Stop All Services
echo ========================================
echo.

echo Stopping backend and frontend...
taskkill /f /im java.exe 2>nul
taskkill /f /im node.exe 2>nul
timeout /t 3 /nobreak >nul

echo ========================================
echo Step 2: Verify Mock Data Removal
echo ========================================
echo.

echo Checking if mock data has been removed...

echo Checking projectService.ts...
findstr /C:"Website Redesign" "frontend\src\services\projectService.ts" >nul
if %ERRORLEVEL% equ 0 (
    echo ❌ FOUND: Mock data still exists in projectService.ts
) else (
    echo ✅ CLEAN: No mock data in projectService.ts
)

echo Checking for localhost URLs in Clients.tsx...
findstr /C:"localhost:8091" "frontend\src\pages\Clients.tsx" >nul
if %ERRORLEVEL% equ 0 (
    echo ❌ FOUND: localhost URLs still exist in Clients.tsx
    findstr /n /C:"localhost:8091" "frontend\src\pages\Clients.tsx"
) else (
    echo ✅ CLEAN: No localhost URLs in Clients.tsx
)

echo ========================================
echo Step 3: Start Backend
echo ========================================
echo.

echo Starting backend...
cd backend

echo Compiling backend...
mvn clean compile -q
if %ERRORLEVEL% neq 0 (
    echo ERROR: Backend compilation failed!
    pause
    exit /b 1
)

echo Starting backend with network access...
start "Backend - No Mock Data" cmd /k "echo Backend starting without mock data... && mvn spring-boot:run"

echo Waiting for backend to start...
timeout /t 20 /nobreak >nul

cd ..

echo ========================================
echo Step 4: Test Backend API
echo ========================================
echo.

echo Testing backend API endpoints...

echo Testing main projects endpoint...
powershell -Command "try { $response = Invoke-RestMethod -Uri 'http://************:8091/projects' -Method Get; Write-Host 'SUCCESS: /projects endpoint working'; Write-Host 'Projects found:' $response.Length; if ($response.Length -gt 0) { Write-Host 'First project:' $response[0].name } } catch { Write-Host 'ERROR: /projects endpoint failed -' $_.Exception.Message }"

echo Testing alternative projects endpoint...
powershell -Command "try { $response = Invoke-RestMethod -Uri 'http://************:8091/projects/getAll' -Method Get; Write-Host 'SUCCESS: /projects/getAll endpoint working'; Write-Host 'Projects found:' $response.Length } catch { Write-Host 'ERROR: /projects/getAll endpoint failed -' $_.Exception.Message }"

echo ========================================
echo Step 5: Start Frontend
echo ========================================
echo.

echo Starting frontend with network access...
cd frontend

echo Checking environment configuration...
if exist ".env" (
    echo Found .env file:
    type .env
) else (
    echo No .env file found, using default network IP
)

echo Starting frontend...
start "Frontend - No Mock Data" cmd /k "echo Frontend starting without mock data... && npm run dev -- --host 0.0.0.0 --port 3060"

echo Waiting for frontend to start...
timeout /t 15 /nobreak >nul

cd ..

echo ========================================
echo Step 6: Test Network Access
echo ========================================
echo.

echo Testing frontend accessibility...
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://************:3060' -Method Get -TimeoutSec 10; Write-Host 'SUCCESS: Frontend accessible from network IP' } catch { Write-Host 'ERROR: Frontend not accessible from network IP:' $_.Exception.Message }"

echo ========================================
echo Step 7: Create Test Page
echo ========================================
echo.

echo Creating comprehensive test page...

:: Create test page
echo ^<!DOCTYPE html^> > test-real-api.html
echo ^<html^> >> test-real-api.html
echo ^<head^> >> test-real-api.html
echo     ^<title^>Real API Test - No Mock Data^</title^> >> test-real-api.html
echo     ^<style^> >> test-real-api.html
echo         body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; } >> test-real-api.html
echo         .container { max-width: 1200px; margin: 0 auto; } >> test-real-api.html
echo         .test-section { margin: 20px 0; padding: 20px; background: white; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); } >> test-real-api.html
echo         .success { border-left: 4px solid #4CAF50; } >> test-real-api.html
echo         .error { border-left: 4px solid #f44336; } >> test-real-api.html
echo         .warning { border-left: 4px solid #ff9800; } >> test-real-api.html
echo         button { padding: 12px 20px; margin: 8px; cursor: pointer; border: none; border-radius: 4px; background: #2196F3; color: white; } >> test-real-api.html
echo         button:hover { background: #1976D2; } >> test-real-api.html
echo         pre { background: #f8f9fa; padding: 15px; border-radius: 4px; overflow-x: auto; } >> test-real-api.html
echo         .mock-warning { background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 4px; margin: 10px 0; } >> test-real-api.html
echo     ^</style^> >> test-real-api.html
echo ^</head^> >> test-real-api.html
echo ^<body^> >> test-real-api.html
echo     ^<div class="container"^> >> test-real-api.html
echo         ^<h1^>🔍 Real API Test - No Mock Data^</h1^> >> test-real-api.html
echo         ^<p^>This page tests the real API endpoints and verifies no mock data is returned.^</p^> >> test-real-api.html
echo         ^<div class="mock-warning"^> >> test-real-api.html
echo             ^<strong^>⚠️ Mock Data Check:^</strong^> If you see projects named "Website Redesign", "ERP Implementation", or clients named "Acme Corporation", then mock data is still being returned! >> test-real-api.html
echo         ^</div^> >> test-real-api.html
echo         ^<div class="test-section"^> >> test-real-api.html
echo             ^<h3^>Test 1: Main Projects API^</h3^> >> test-real-api.html
echo             ^<button onclick="testEndpoint('http://************:8091/projects', 'result1')"^>Test /projects^</button^> >> test-real-api.html
echo             ^<div id="result1"^>^</div^> >> test-real-api.html
echo         ^</div^> >> test-real-api.html
echo         ^<div class="test-section"^> >> test-real-api.html
echo             ^<h3^>Test 2: GetAll Projects API^</h3^> >> test-real-api.html
echo             ^<button onclick="testEndpoint('http://************:8091/projects/getAll', 'result2')"^>Test /projects/getAll^</button^> >> test-real-api.html
echo             ^<div id="result2"^>^</div^> >> test-real-api.html
echo         ^</div^> >> test-real-api.html
echo         ^<div class="test-section"^> >> test-real-api.html
echo             ^<h3^>Test 3: Frontend Application^</h3^> >> test-real-api.html
echo             ^<button onclick="window.open('http://************:3060/simple-projects', '_blank')"^>Open Simple Projects Page^</button^> >> test-real-api.html
echo             ^<p^>This should open the Simple Projects page. Check if it shows real data or errors (not mock data).^</p^> >> test-real-api.html
echo         ^</div^> >> test-real-api.html
echo         ^<div class="test-section"^> >> test-real-api.html
echo             ^<h3^>Test All APIs^</h3^> >> test-real-api.html
echo             ^<button onclick="testAllAPIs()"^>Test All Endpoints^</button^> >> test-real-api.html
echo             ^<div id="resultAll"^>^</div^> >> test-real-api.html
echo         ^</div^> >> test-real-api.html
echo     ^</div^> >> test-real-api.html
echo     ^<script^> >> test-real-api.html
echo         function testEndpoint(url, resultId) { >> test-real-api.html
echo             const result = document.getElementById(resultId); >> test-real-api.html
echo             result.innerHTML = '^<p^>Testing ' + url + '...^</p^>'; >> test-real-api.html
echo             result.className = 'test-section'; >> test-real-api.html
echo             fetch(url) >> test-real-api.html
echo                 .then(response =^> { >> test-real-api.html
echo                     if (response.ok) { >> test-real-api.html
echo                         return response.json(); >> test-real-api.html
echo                     } else { >> test-real-api.html
echo                         throw new Error('HTTP ' + response.status + ': ' + response.statusText); >> test-real-api.html
echo                     } >> test-real-api.html
echo                 }) >> test-real-api.html
echo                 .then(data =^> { >> test-real-api.html
echo                     result.className = 'test-section success'; >> test-real-api.html
echo                     let mockDataFound = false; >> test-real-api.html
echo                     let mockDataWarning = ''; >> test-real-api.html
echo                     if (Array.isArray(data)) { >> test-real-api.html
echo                         data.forEach(item =^> { >> test-real-api.html
echo                             if (item.name === 'Website Redesign' ^|^| item.name === 'ERP Implementation' ^|^| item.name === 'Mobile App Development') { >> test-real-api.html
echo                                 mockDataFound = true; >> test-real-api.html
echo                             } >> test-real-api.html
echo                             if (item.client ^&^& (item.client.name === 'Acme Corporation' ^|^| item.client.name === 'Globex Industries' ^|^| item.client.name === 'Initech Solutions')) { >> test-real-api.html
echo                                 mockDataFound = true; >> test-real-api.html
echo                             } >> test-real-api.html
echo                         }); >> test-real-api.html
echo                     } >> test-real-api.html
echo                     if (mockDataFound) { >> test-real-api.html
echo                         mockDataWarning = '^<div style="background: #ffebee; border: 1px solid #f44336; padding: 10px; margin: 10px 0; border-radius: 4px;"^>^<strong^>❌ MOCK DATA DETECTED!^</strong^> This response contains mock data instead of real database data.^</div^>'; >> test-real-api.html
echo                         result.className = 'test-section warning'; >> test-real-api.html
echo                     } >> test-real-api.html
echo                     result.innerHTML = '^<h4^>✅ SUCCESS!^</h4^>' + mockDataWarning + >> test-real-api.html
echo                         '^<p^>Found ' + (Array.isArray(data) ? data.length : 1) + ' item(s)^</p^>' + >> test-real-api.html
echo                         '^<pre^>' + JSON.stringify(data, null, 2) + '^</pre^>'; >> test-real-api.html
echo                 }) >> test-real-api.html
echo                 .catch(error =^> { >> test-real-api.html
echo                     result.className = 'test-section error'; >> test-real-api.html
echo                     result.innerHTML = '^<h4^>❌ ERROR^</h4^>^<p^>' + error.message + '^</p^>'; >> test-real-api.html
echo                 }); >> test-real-api.html
echo         } >> test-real-api.html
echo         function testAllAPIs() { >> test-real-api.html
echo             testEndpoint('http://************:8091/projects', 'result1'); >> test-real-api.html
echo             setTimeout(() =^> testEndpoint('http://************:8091/projects/getAll', 'result2'), 1000); >> test-real-api.html
echo         } >> test-real-api.html
echo     ^</script^> >> test-real-api.html
echo ^</body^> >> test-real-api.html
echo ^</html^> >> test-real-api.html

echo ✅ Created test-real-api.html

echo ========================================
echo TESTING RESULTS
echo ========================================
echo.

echo Mock data and localhost issues have been fixed!
echo.

echo Test from ANOTHER PC:
echo.

echo 1. API Direct Test:
echo    Open browser: http://************:8091/projects
echo    Expected: Real database data (not mock "Website Redesign")
echo.

echo 2. Comprehensive Test Page:
echo    Copy test-real-api.html to another PC
echo    Open in browser and test all endpoints
echo    Expected: Real data or clear errors (no mock data warnings)
echo.

echo 3. Frontend Application:
echo    Open browser: http://************:3060/simple-projects
echo    Expected: Real project data or clear error messages
echo.

echo ========================================
echo EXPECTED RESULTS
echo ========================================
echo.

echo ✅ When working correctly:
echo   - API returns real database projects (your actual project names)
echo   - No "Website Redesign", "Acme Corporation", etc. mock data
echo   - Frontend shows real data or clear error messages
echo   - No "localhost:8091" connection errors
echo.

echo ❌ If you still see issues:
echo   - Mock data: Check browser cache (Ctrl+F5 to hard refresh)
echo   - Connection errors: Check Windows Firewall settings
echo   - 500 errors: Check backend console for database issues
echo.

echo ========================================
echo.

echo Fix complete! Test the URLs above from another PC.
echo No more mock data should appear!
echo.

pause
