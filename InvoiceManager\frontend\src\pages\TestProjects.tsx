import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Loader2, RefreshCcw } from "lucide-react";

const TestProjects = () => {
  const [projects, setProjects] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [rawResponse, setRawResponse] = useState<string>("");

  const fetchProjects = async () => {
    setLoading(true);
    setError(null);
    setRawResponse("");

    try {
      console.log("TestProjects: Fetching projects from API...");
      
      // Use environment variable for server URL
      const serverUrl = import.meta.env.VITE_API_BASE_URL || 'http://192.168.1.30:8091';
      const endpoint = `${serverUrl}/projects`;

      console.log("TestProjects: Using endpoint:", endpoint);

      const response = await fetch(endpoint, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json'
        }
      });

      console.log("TestProjects: Response status:", response.status);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      console.log("TestProjects: Raw API response:", data);
      
      setRawResponse(JSON.stringify(data, null, 2));

      if (Array.isArray(data)) {
        setProjects(data);
        console.log("TestProjects: Set projects array with length:", data.length);
      } else {
        setProjects([data]);
        console.log("TestProjects: Set single project as array");
      }

    } catch (err) {
      console.error("TestProjects: Error fetching projects:", err);
      setError(err instanceof Error ? err.message : String(err));
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchProjects();
  }, []);

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Test Projects API</h1>
        <Button onClick={fetchProjects} disabled={loading}>
          {loading ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <RefreshCcw className="mr-2 h-4 w-4" />}
          Refresh
        </Button>
      </div>

      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardHeader>
            <CardTitle className="text-red-800">Error</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-red-700">{error}</p>
          </CardContent>
        </Card>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Projects Data ({projects.length} items)</CardTitle>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="flex items-center justify-center p-8">
                <Loader2 className="h-8 w-8 animate-spin" />
                <span className="ml-2">Loading projects...</span>
              </div>
            ) : projects.length > 0 ? (
              <div className="space-y-4">
                {projects.map((project, index) => (
                  <div key={project.id || index} className="border rounded p-4 bg-gray-50">
                    <h3 className="font-semibold text-lg">{project.name || 'Unnamed Project'}</h3>
                    <div className="grid grid-cols-2 gap-2 mt-2 text-sm">
                      <div><strong>ID:</strong> {project.id}</div>
                      <div><strong>Client:</strong> {project.client?.name || project.clientId || 'N/A'}</div>
                      <div><strong>Email:</strong> {project.email || 'N/A'}</div>
                      <div><strong>Phone:</strong> {project.phone || 'N/A'}</div>
                      <div><strong>GST:</strong> {project.gstNumber || 'N/A'}</div>
                      <div><strong>State:</strong> {project.state || 'N/A'}</div>
                      <div><strong>Value:</strong> {project.value || 'N/A'}</div>
                      <div><strong>Status:</strong> {project.status || 'N/A'}</div>
                      <div><strong>HSN Code:</strong> {project.hsnCode?.code || 'N/A'}</div>
                      <div><strong>BDM:</strong> {project.bdm?.name || 'N/A'}</div>
                      <div><strong>Created:</strong> {project.created_at || project.createdAt || 'N/A'}</div>
                      <div><strong>Updated:</strong> {project.updated_at || project.updatedAt || 'N/A'}</div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-gray-500 text-center p-8">No projects found</p>
            )}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Raw API Response</CardTitle>
          </CardHeader>
          <CardContent>
            {rawResponse ? (
              <pre className="bg-gray-100 p-4 rounded text-xs overflow-auto max-h-96">
                {rawResponse}
              </pre>
            ) : (
              <p className="text-gray-500 text-center p-8">No response data</p>
            )}
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>API Test Results</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <div className="flex items-center space-x-2">
              <span className={`w-3 h-3 rounded-full ${!error ? 'bg-green-500' : 'bg-red-500'}`}></span>
              <span>API Connection: {!error ? 'Success' : 'Failed'}</span>
            </div>
            <div className="flex items-center space-x-2">
              <span className={`w-3 h-3 rounded-full ${projects.length > 0 ? 'bg-green-500' : 'bg-yellow-500'}`}></span>
              <span>Data Retrieved: {projects.length > 0 ? `${projects.length} projects` : 'No data'}</span>
            </div>
            <div className="flex items-center space-x-2">
              <span className={`w-3 h-3 rounded-full ${projects.some(p => p.name) ? 'bg-green-500' : 'bg-yellow-500'}`}></span>
              <span>Data Quality: {projects.some(p => p.name) ? 'Good (has names)' : 'Needs review'}</span>
            </div>
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Debug Information</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-sm space-y-1">
            <div><strong>Endpoint:</strong> http://192.168.1.30:8091/projects</div>
            <div><strong>Method:</strong> GET</div>
            <div><strong>Headers:</strong> Accept: application/json, Content-Type: application/json</div>
            <div><strong>Expected Status:</strong> 200</div>
            <div><strong>Expected Format:</strong> Array of project objects</div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default TestProjects;
