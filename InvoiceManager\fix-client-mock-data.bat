@echo off
echo ========================================
echo FIXING CLIENT MOCK DATA ISSUE
echo ========================================
echo.

echo Problem: Project data is correct but client data shows mock data
echo Solution: Remove client mock data and use working client endpoints
echo.

echo ========================================
echo Step 1: Test Client Endpoints
echo ========================================
echo.

echo Testing client API endpoints to find working one...

echo Testing /clients...
powershell -Command "try { $response = Invoke-RestMethod -Uri 'http://************:8091/clients' -Method Get; Write-Host '/clients: SUCCESS - Clients:' $response.Length; if ($response.Length -gt 0) { Write-Host 'First client:' $response[0].name } } catch { Write-Host '/clients: FAILED -' $_.Exception.Message }"

echo Testing /clients/getAll...
powershell -Command "try { $response = Invoke-RestMethod -Uri 'http://************:8091/clients/getAll' -Method Get; Write-Host '/clients/getAll: SUCCESS - Clients:' $response.Length } catch { Write-Host '/clients/getAll: FAILED -' $_.Exception.Message }"

echo Testing /clients with auth...
powershell -Command "$auth = [Convert]::ToBase64String([Text.Encoding]::ASCII.GetBytes('admin:admin123')); try { $response = Invoke-RestMethod -Uri 'http://************:8091/clients' -Method Get -Headers @{'Authorization'='Basic '+$auth}; Write-Host '/clients (with auth): SUCCESS - Clients:' $response.Length; if ($response.Length -gt 0) { Write-Host 'First client:' $response[0].name } } catch { Write-Host '/clients (with auth): FAILED -' $_.Exception.Message }"

echo Testing /clients/getAll with auth...
powershell -Command "$auth = [Convert]::ToBase64String([Text.Encoding]::ASCII.GetBytes('admin:admin123')); try { $response = Invoke-RestMethod -Uri 'http://************:8091/clients/getAll' -Method Get -Headers @{'Authorization'='Basic '+$auth}; Write-Host '/clients/getAll (with auth): SUCCESS - Clients:' $response.Length; if ($response.Length -gt 0) { Write-Host 'First client:' $response[0].name } } catch { Write-Host '/clients/getAll (with auth): FAILED -' $_.Exception.Message }"

echo Testing /api/clients with auth...
powershell -Command "$auth = [Convert]::ToBase64String([Text.Encoding]::ASCII.GetBytes('admin:admin123')); try { $response = Invoke-RestMethod -Uri 'http://************:8091/api/clients' -Method Get -Headers @{'Authorization'='Basic '+$auth}; Write-Host '/api/clients (with auth): SUCCESS - Clients:' $response.Length } catch { Write-Host '/api/clients (with auth): FAILED -' $_.Exception.Message }"

echo ========================================
echo Step 2: Stop Services
echo ========================================
echo.

echo Stopping backend and frontend...
taskkill /f /im java.exe 2>nul
taskkill /f /im node.exe 2>nul
timeout /t 3 /nobreak >nul

echo ========================================
echo Step 3: Start Backend
echo ========================================
echo.

echo Starting backend...
cd backend

echo Starting backend...
start "Backend - Client Fix" cmd /k "echo Backend starting with client fix... && mvn spring-boot:run"

echo Waiting for backend to start...
timeout /t 20 /nobreak >nul

cd ..

echo ========================================
echo Step 4: Test Working Client Endpoint
echo ========================================
echo.

echo Re-testing client endpoints after restart...

echo Testing /clients with auth (most likely to work)...
powershell -Command "$auth = [Convert]::ToBase64String([Text.Encoding]::ASCII.GetBytes('admin:admin123')); try { $response = Invoke-RestMethod -Uri 'http://************:8091/clients' -Method Get -Headers @{'Authorization'='Basic '+$auth}; Write-Host 'SUCCESS: /clients with auth works!'; Write-Host 'Clients found:' $response.Length; if ($response.Length -gt 0) { Write-Host 'Real client data:'; $response | ForEach-Object { Write-Host '  -' $_.name '(ID:' $_.id ')' } } else { Write-Host 'No clients in database' } } catch { Write-Host 'FAILED: /clients with auth -' $_.Exception.Message }"

echo ========================================
echo Step 5: Start Frontend
echo ========================================
echo.

echo Starting frontend with client fix...
cd frontend

echo Starting frontend...
start "Frontend - Client Fix" cmd /k "echo Frontend starting with client fix... && npm run dev -- --host 0.0.0.0 --port 3060"

echo Waiting for frontend to start...
timeout /t 15 /nobreak >nul

cd ..

echo ========================================
echo Step 6: Create Client Test Page
echo ========================================
echo.

echo Creating client test page...

:: Create test page
echo ^<!DOCTYPE html^> > test-client-data.html
echo ^<html^> >> test-client-data.html
echo ^<head^> >> test-client-data.html
echo     ^<title^>Client Data Test^</title^> >> test-client-data.html
echo     ^<style^> >> test-client-data.html
echo         body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; } >> test-client-data.html
echo         .container { max-width: 1000px; margin: 0 auto; } >> test-client-data.html
echo         .test-section { margin: 20px 0; padding: 20px; background: white; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); } >> test-client-data.html
echo         .success { border-left: 4px solid #4CAF50; } >> test-client-data.html
echo         .error { border-left: 4px solid #f44336; } >> test-client-data.html
echo         .warning { border-left: 4px solid #ff9800; } >> test-client-data.html
echo         button { padding: 12px 20px; margin: 8px; cursor: pointer; border: none; border-radius: 4px; background: #2196F3; color: white; } >> test-client-data.html
echo         button:hover { background: #1976D2; } >> test-client-data.html
echo         pre { background: #f8f9fa; padding: 15px; border-radius: 4px; overflow-x: auto; max-height: 300px; } >> test-client-data.html
echo         .mock-warning { background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 4px; margin: 10px 0; } >> test-client-data.html
echo     ^</style^> >> test-client-data.html
echo ^</head^> >> test-client-data.html
echo ^<body^> >> test-client-data.html
echo     ^<div class="container"^> >> test-client-data.html
echo         ^<h1^>👥 Client Data Test^</h1^> >> test-client-data.html
echo         ^<p^>This page tests client API endpoints and checks for mock data.^</p^> >> test-client-data.html
echo         ^<div class="mock-warning"^> >> test-client-data.html
echo             ^<strong^>⚠️ Mock Data Check:^</strong^> If you see clients named "ABC Technologies", "XYZ Solutions", or "Acme Corporation", then mock data is being returned! >> test-client-data.html
echo         ^</div^> >> test-client-data.html
echo         ^<div class="test-section"^> >> test-client-data.html
echo             ^<h3^>Test 1: Clients API (with auth)^</h3^> >> test-client-data.html
echo             ^<button onclick="testClientsWithAuth()"^>Test /clients (with auth)^</button^> >> test-client-data.html
echo             ^<div id="result1"^>^</div^> >> test-client-data.html
echo         ^</div^> >> test-client-data.html
echo         ^<div class="test-section"^> >> test-client-data.html
echo             ^<h3^>Test 2: Clients API (no auth)^</h3^> >> test-client-data.html
echo             ^<button onclick="testClientsNoAuth()"^>Test /clients (no auth)^</button^> >> test-client-data.html
echo             ^<div id="result2"^>^</div^> >> test-client-data.html
echo         ^</div^> >> test-client-data.html
echo         ^<div class="test-section"^> >> test-client-data.html
echo             ^<h3^>Test 3: Frontend Application^</h3^> >> test-client-data.html
echo             ^<button onclick="window.open('http://************:3060', '_blank')"^>Open Clients Page^</button^> >> test-client-data.html
echo             ^<p^>Check if the clients page shows real client data (not mock data).^</p^> >> test-client-data.html
echo         ^</div^> >> test-client-data.html
echo         ^<div class="test-section"^> >> test-client-data.html
echo             ^<h3^>Test All Client Endpoints^</h3^> >> test-client-data.html
echo             ^<button onclick="testAllClientEndpoints()"^>Test All Endpoints^</button^> >> test-client-data.html
echo             ^<div id="resultAll"^>^</div^> >> test-client-data.html
echo         ^</div^> >> test-client-data.html
echo     ^</div^> >> test-client-data.html
echo     ^<script^> >> test-client-data.html
echo         function testClientsWithAuth() { >> test-client-data.html
echo             const result = document.getElementById('result1'); >> test-client-data.html
echo             result.innerHTML = '^<p^>Testing clients with auth...^</p^>'; >> test-client-data.html
echo             result.className = 'test-section'; >> test-client-data.html
echo             const auth = btoa('admin:admin123'); >> test-client-data.html
echo             fetch('http://************:8091/clients', { >> test-client-data.html
echo                 method: 'GET', >> test-client-data.html
echo                 headers: { >> test-client-data.html
echo                     'Accept': 'application/json', >> test-client-data.html
echo                     'Content-Type': 'application/json', >> test-client-data.html
echo                     'Authorization': 'Basic ' + auth >> test-client-data.html
echo                 } >> test-client-data.html
echo             }) >> test-client-data.html
echo                 .then(response =^> { >> test-client-data.html
echo                     if (response.ok) { >> test-client-data.html
echo                         return response.json(); >> test-client-data.html
echo                     } else { >> test-client-data.html
echo                         throw new Error('HTTP ' + response.status + ': ' + response.statusText); >> test-client-data.html
echo                     } >> test-client-data.html
echo                 }) >> test-client-data.html
echo                 .then(data =^> { >> test-client-data.html
echo                     result.className = 'test-section success'; >> test-client-data.html
echo                     let mockDataFound = false; >> test-client-data.html
echo                     if (Array.isArray(data)) { >> test-client-data.html
echo                         data.forEach(client =^> { >> test-client-data.html
echo                             if (client.name === 'ABC Technologies Pvt Ltd' ^|^| client.name === 'XYZ Solutions' ^|^| client.name === 'Acme Corporation') { >> test-client-data.html
echo                                 mockDataFound = true; >> test-client-data.html
echo                             } >> test-client-data.html
echo                         }); >> test-client-data.html
echo                     } >> test-client-data.html
echo                     let mockWarning = ''; >> test-client-data.html
echo                     if (mockDataFound) { >> test-client-data.html
echo                         mockWarning = '^<div style="background: #ffebee; border: 1px solid #f44336; padding: 10px; margin: 10px 0; border-radius: 4px;"^>^<strong^>❌ MOCK DATA DETECTED!^</strong^> This response contains mock client data.^</div^>'; >> test-client-data.html
echo                         result.className = 'test-section warning'; >> test-client-data.html
echo                     } >> test-client-data.html
echo                     result.innerHTML = '^<h4^>✅ SUCCESS!^</h4^>' + mockWarning + >> test-client-data.html
echo                         '^<p^>Found ' + (Array.isArray(data) ? data.length : 1) + ' client(s)^</p^>' + >> test-client-data.html
echo                         '^<pre^>' + JSON.stringify(data, null, 2) + '^</pre^>'; >> test-client-data.html
echo                 }) >> test-client-data.html
echo                 .catch(error =^> { >> test-client-data.html
echo                     result.className = 'test-section error'; >> test-client-data.html
echo                     result.innerHTML = '^<h4^>❌ ERROR^</h4^>^<p^>' + error.message + '^</p^>'; >> test-client-data.html
echo                 }); >> test-client-data.html
echo         } >> test-client-data.html
echo         function testClientsNoAuth() { >> test-client-data.html
echo             const result = document.getElementById('result2'); >> test-client-data.html
echo             result.innerHTML = '^<p^>Testing clients without auth...^</p^>'; >> test-client-data.html
echo             result.className = 'test-section'; >> test-client-data.html
echo             fetch('http://************:8091/clients') >> test-client-data.html
echo                 .then(response =^> { >> test-client-data.html
echo                     if (response.ok) { >> test-client-data.html
echo                         return response.json(); >> test-client-data.html
echo                     } else { >> test-client-data.html
echo                         throw new Error('HTTP ' + response.status + ': ' + response.statusText); >> test-client-data.html
echo                     } >> test-client-data.html
echo                 }) >> test-client-data.html
echo                 .then(data =^> { >> test-client-data.html
echo                     result.className = 'test-section success'; >> test-client-data.html
echo                     result.innerHTML = '^<h4^>✅ SUCCESS!^</h4^>' + >> test-client-data.html
echo                         '^<p^>Found ' + (Array.isArray(data) ? data.length : 1) + ' client(s)^</p^>' + >> test-client-data.html
echo                         '^<pre^>' + JSON.stringify(data, null, 2) + '^</pre^>'; >> test-client-data.html
echo                 }) >> test-client-data.html
echo                 .catch(error =^> { >> test-client-data.html
echo                     result.className = 'test-section error'; >> test-client-data.html
echo                     result.innerHTML = '^<h4^>❌ EXPECTED ERROR^</h4^>^<p^>' + error.message + '^</p^>'; >> test-client-data.html
echo                 }); >> test-client-data.html
echo         } >> test-client-data.html
echo         function testAllClientEndpoints() { >> test-client-data.html
echo             testClientsWithAuth(); >> test-client-data.html
echo             setTimeout(() =^> testClientsNoAuth(), 1000); >> test-client-data.html
echo         } >> test-client-data.html
echo     ^</script^> >> test-client-data.html
echo ^</body^> >> test-client-data.html
echo ^</html^> >> test-client-data.html

echo ✅ Created test-client-data.html

echo ========================================
echo TESTING RESULTS
echo ========================================
echo.

echo Client mock data has been removed!
echo.

echo Test from ANOTHER PC:
echo.

echo 1. Client API Test:
echo    Copy test-client-data.html to another PC
echo    Open in browser and test client endpoints
echo    Expected: Real client data (not mock "ABC Technologies")
echo.

echo 2. Frontend Application:
echo    Open browser: http://************:3060
echo    Go to Clients page
echo    Expected: Real client data from your database
echo.

echo 3. Direct API Test:
echo    Open browser: http://************:8091/clients
echo    Expected: May prompt for login, then show real client JSON
echo.

echo ========================================
echo EXPECTED RESULTS
echo ========================================
echo.

echo ✅ When working correctly:
echo   - Clients page shows real client data from database
echo   - No mock "ABC Technologies", "XYZ Solutions", "Acme Corporation"
echo   - Shows your actual client names (like "saurabh")
echo   - Both projects AND clients show real data
echo.

echo ❌ If still showing mock data:
echo   - Check browser cache (Ctrl+F5 to hard refresh)
echo   - Check backend console for client API errors
echo   - Verify client data exists in database: SELECT * FROM clients;
echo.

echo ========================================
echo.

echo Client mock data fix complete!
echo Test from another PC - both projects and clients should show real data!
echo.

pause
