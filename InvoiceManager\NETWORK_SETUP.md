# Invoice Manager - Network Setup Guide

This guide will help you configure the Invoice Manager application to be accessible from other devices on your network.

## Quick Start

### Option 1: Automatic Setup (Recommended)
```bash
# Run the network configuration script
.\start-network.bat
```
or
```powershell
.\start-network.ps1
```

### Option 2: Manual Configuration
```bash
# Configure network settings
.\scripts\configure-network.bat

# Start backend (in one terminal)
cd backend
mvn spring-boot:run

# Start frontend (in another terminal)
cd frontend
npm run dev
```

## Your Current Network Configuration

**Detected IP Address:** `************`

### Access URLs
- **Local (on your machine):** http://localhost:3060
- **Network (from other devices):** http://************:3060

## Firewall Configuration

### Automatic Firewall Setup (Run as Administrator)
```cmd
netsh advfirewall firewall add rule name="Invoice App Frontend" dir=in action=allow protocol=TCP localport=3060
netsh advfirewall firewall add rule name="Invoice App Backend" dir=in action=allow protocol=TCP localport=8091
```

### Manual Firewall Setup
1. Open Windows Defender Firewall
2. Click "Advanced settings"
3. Click "Inbound Rules" → "New Rule"
4. Select "Port" → "TCP" → "Specific local ports"
5. Enter `3060` for frontend
6. Repeat for port `8091` for backend

## Network Scripts

### Configuration Scripts
- `scripts/configure-network.ps1` - PowerShell configuration script
- `scripts/configure-network.bat` - Batch configuration script
- `frontend/setup-env.js` - Frontend environment setup

### Startup Scripts
- `start-network.ps1` - PowerShell startup script (recommended)
- `start-network.bat` - Batch startup script

### Usage Examples
```powershell
# Auto-detect IP and configure
.\scripts\configure-network.ps1

# Use localhost only
.\scripts\configure-network.ps1 -UseLocalhost

# Use specific IP
.\scripts\configure-network.ps1 -CustomIP *************

# Start with custom IP
.\start-network.ps1 -CustomIP *************
```

## Troubleshooting

### Common Issues

#### 1. "Connection Refused" from other devices
**Solution:** Check Windows Firewall settings
```cmd
# Check if ports are open
netstat -an | findstr :3060
netstat -an | findstr :8091
```

#### 2. Backend API calls failing
**Solution:** Verify environment configuration
```bash
# Check frontend .env file
cat frontend/.env

# Should show: VITE_API_URL=http://************:8091/api
```

#### 3. IP address changed
**Solution:** Reconfigure network settings
```bash
# Get new IP
ipconfig

# Reconfigure with new IP
.\scripts\configure-network.ps1 -CustomIP <NEW_IP>
```

#### 4. Services not starting
**Solution:** Check prerequisites
```bash
# Check Java
java -version

# Check Node.js
node --version

# Check Maven
mvn --version
```

### Network Diagnostics
```bash
# Check network connectivity
ping ************

# Check if services are running
netstat -an | findstr :3060
netstat -an | findstr :8091

# Test backend API
curl http://************:8091/api/health
```

## Security Considerations

### Network Security
- The application is configured to accept connections from any IP on your local network
- Make sure you trust all devices on your network
- Consider using a VPN for remote access instead of exposing to the internet

### Firewall Rules
- Only ports 3060 and 8091 need to be open
- Rules are configured for local network access only
- No internet exposure by default

## Advanced Configuration

### Custom Port Configuration
To use different ports, modify these files:
- `backend/src/main/resources/application.properties` - Change `server.port`
- `frontend/vite.config.ts` - Change `server.port`
- Update firewall rules accordingly

### Environment-Specific Configuration
- `.env.local` - Local development
- `.env.production` - Production deployment
- `.env` - Current active configuration

### Database Network Access
If you need database access from other machines:
```properties
# In application.properties
spring.datasource.url=**********************************************
```

## Support

If you encounter issues:
1. Check the troubleshooting section above
2. Verify all prerequisites are installed
3. Ensure firewall rules are configured
4. Check network connectivity between devices

For additional help, check the application logs:
- Backend logs: Console output from `mvn spring-boot:run`
- Frontend logs: Browser developer console
