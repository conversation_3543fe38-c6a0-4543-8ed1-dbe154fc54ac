<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <title>RedBeryl Tech Solutions - Invoice</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap');

        body {
            font-family: '<PERSON>o', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            color: #333;
            background-color: #f9f9f9;
            font-size: 14px;
            line-height: 1.5;
        }

        .invoice-container {
            max-width: 800px;
            margin: 0 auto;
            border: 1px solid #ddd;
            padding: 30px;
            background-color: white;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            background: linear-gradient(to bottom, #ffffff, #f9f9f9);
        }

        .invoice-header {
            color: #0066cc;
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
            border-bottom: 2px solid #0066cc;
            padding-bottom: 10px;
            letter-spacing: 0.5px;
        }

        .company-info {
            margin-top: 20px;
            margin-bottom: 30px;
        }

        .invoice-details {
            float: right;
            width: 250px;
            border-collapse: collapse;
            margin-bottom: 20px;
        }

        .invoice-details th, .invoice-details td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }

        .invoice-details th {
            background-color: #f2f2f2;
        }

        .bill-to {
            border: 1px solid #ddd;
            padding: 15px;
            margin-bottom: 20px;
            width: 300px;
            clear: both;
        }

        .bill-to-title {
            font-weight: bold;
            margin-bottom: 10px;
        }

        .items-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }

        .items-table th, .items-table td {
            border: 1px solid #ddd;
            padding: 10px;
            text-align: left;
        }

        .items-table th {
            background-color: #f2f2f2;
            font-weight: 500;
            color: #333;
            border-bottom: 2px solid #ddd;
        }

        .items-table .amount {
            text-align: right;
        }

        .totals-table {
            width: 300px;
            float: right;
            border-collapse: collapse;
            margin-bottom: 30px;
        }

        .totals-table td {
            border: 1px solid #ddd;
            padding: 8px;
        }

        .totals-table tr td:first-child {
            text-align: left;
        }

        .totals-table tr td:last-child {
            text-align: right;
        }

        .terms {
            clear: both;
            border: 1px solid #ddd;
            padding: 15px;
            background-color: #f9f9f9;
            margin-bottom: 20px;
        }

        .footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 10px;
            border-top: 2px solid #0066cc;
            color: #777;
            font-size: 12px;
        }

        /* Clearfix */
        .clearfix::after {
            content: "";
            clear: both;
            display: table;
        }

        /* Print styles */
        @media print {
            body {
                background-color: white;
                padding: 0;
            }

            .invoice-container {
                box-shadow: none;
                border: none;
                padding: 20px;
                max-width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="invoice-container">
        <div class="invoice-header" th:text="${companyName} + ' Invoice'">RedBeryl Tech Solutions Invoice</div>

        <div class="company-info">
            <div th:text="${companyName}">RedBeryl Tech Solutions</div>
            <div th:text="${companyAddress}">507-B Amanora Chambers</div>
            <div th:text="${companyCity}">Amanora Mall, Hadapsar, Pune 411028</div>
            <div th:text="${companyPhone}">+91 **********</div>
        </div>

        <table class="invoice-details">
            <tr>
                <th>Invoice #</th>
                <td th:text="${invoiceNumber}">INV-001</td>
            </tr>
            <tr>
                <th>Issue Date</th>
                <td th:text="${invoiceDate}">05/13/2025</td>
            </tr>
            <tr>
                <th>Due Date</th>
                <td th:text="${dueDate}">05/14/2025</td>
            </tr>
            <tr th:if="${invoiceType}">
                <th>Invoice Type</th>
                <td th:text="${invoiceType}">debit</td>
            </tr>
            <tr th:if="${staffingType}">
                <th>Staffing Type</th>
                <td th:text="${staffingType}">Full Time</td>
            </tr>
            <tr th:if="${hsnCode}">
                <th>HSN Code</th>
                <td th:text="${hsnCode}">998313</td>
            </tr>
        </table>

        <div class="clearfix"></div>

        <div class="bill-to">
            <div class="bill-to-title">Bill To</div>
            <!-- Client Information -->
            <div th:text="${clientName}">prathamesh kadam</div>

            <!-- Project Information -->
            <div th:if="${projectName}" th:text="${projectName}">Website Development</div>
            <div th:if="${projectBillingAddress}" th:text="${projectBillingAddress}">123 Client Street, Mumbai, 400001</div>
            <div th:if="${projectEmail}" th:text="${projectEmail}"><EMAIL></div>
            <div th:if="${projectPhone}" th:text="${projectPhone}">+91 **********</div>
            <div th:if="${projectGstNumber}" th:text="${'GSTIN: ' + projectGstNumber}">GSTIN: 27AAAAA0000A1Z5</div>

            <!-- Additional Project Information -->
            <div th:if="${projectEngagementCode}" th:text="${'Engagement Code: ' + projectEngagementCode}">Engagement Code: ENG-001</div>
            <div th:if="${projectClientPartnerName}" th:text="${'Client Partner: ' + projectClientPartnerName}">Client Partner: John Doe</div>

            <!-- BDM Information -->
            <div th:if="${bdmName}" th:text="${'BDM: ' + bdmName}">BDM: Jane Smith</div>
        </div>

        <table class="items-table">
            <thead>
                <tr>
                    <th style="width: 25%;">Item</th>
                    <th style="width: 55%;">Description</th>
                    <th style="width: 20%;" class="amount">Amount</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>
                        <div th:text="${invoiceType ?: 'debit'}">debit</div>
                        <div th:if="${hsnCode}" style="font-size: 12px; color: #666;" th:text="${'HSN: ' + hsnCode}">HSN: 998313</div>
                    </td>
                    <td>
                        <div th:text="${projectName ?: 'Website Development'}">Website Development</div>
                        <div th:if="${description}" style="font-size: 12px; color: #666;" th:text="${description}">Invoice description</div>
                        <div th:if="${projectDescription}" style="font-size: 12px; color: #666;" th:text="${projectDescription}">Professional web development services</div>
                        <div th:if="${candidateName}" style="font-size: 12px; color: #666;">
                            <span th:text="${'Candidate: ' + candidateName}">Candidate: John Doe</span>
                            <span th:if="${candidateDesignation}" th:text="${' (' + candidateDesignation + ')'}"> (Developer)</span>
                        </div>
                    </td>
                    <td class="amount" th:text="${billingAmount}">$3,000.00</td>
                </tr>
            </tbody>
        </table>

        <table class="totals-table">
            <tr style="font-weight: bold; background-color: #f2f2f2;">
                <td>Total Amount</td>
                <td th:text="${totalAmount}">$5,000.00</td>
            </tr>
        </table>

        <div class="clearfix"></div>

        <div class="terms">
            <!-- Payment terms removed as requested -->

            <!-- Redberyl Account Information -->
            <div th:if="${redberylAccountName} or ${redberylAccountBankName}" style="margin-top: 15px; border-top: 1px solid #ddd; padding-top: 10px;">
                <div style="font-weight: bold; margin-bottom: 5px;">Payment Information:</div>
                <div th:if="${redberylAccountBankName}" th:text="${'Bank Name: ' + redberylAccountBankName}">Bank Name: HDFC Bank</div>
                <div th:if="${redberylAccountName}" th:text="${'Account Name: ' + redberylAccountName}">Account Name: RedBeryl Tech Solutions</div>
                <div th:if="${redberylAccountNumber}" th:text="${'Account Number: ' + redberylAccountNumber}">Account Number: **************</div>
                <div th:if="${redberylAccountIfscCode}" th:text="${'IFSC Code: ' + redberylAccountIfscCode}">IFSC Code: HDFC0001234</div>
                <div th:if="${redberylAccountBranchName}" th:text="${'Branch: ' + redberylAccountBranchName}">Branch: Hadapsar, Pune</div>
                <div th:if="${redberylAccountGstn}" th:text="${'GSTN: ' + redberylAccountGstn}">GSTN: 27**********1Z5</div>
                <div th:if="${redberylAccountCin}" th:text="${'CIN: ' + redberylAccountCin}">CIN: U72900PN2023PTC123456</div>
                <div th:if="${redberylAccountPanNo}" th:text="${'PAN: ' + redberylAccountPanNo}">PAN: **********</div>
            </div>

            <!-- Project Details Section -->
            <div th:if="${project}" style="margin-top: 15px; border-top: 1px solid #ddd; padding-top: 10px;">
                <div style="font-weight: bold; margin-bottom: 5px;">Project Details:</div>
                <div th:if="${projectStartDate}" th:text="${'Start Date: ' + projectStartDate}">Start Date: 01/01/2025</div>
                <div th:if="${projectEndDate}" th:text="${'End Date: ' + projectEndDate}">End Date: 12/31/2025</div>
                <div th:if="${projectStatus}" th:text="${'Status: ' + projectStatus}">Status: Active</div>
                <div th:if="${projectValue}" th:text="${'Project Value: ' + projectValue}">Project Value: $50,000.00</div>
                <div th:if="${projectDescription}" th:text="${'Description: ' + projectDescription}">Description: Professional web development services</div>
                <div th:if="${projectEngagementCode}" th:text="${'Engagement Code: ' + projectEngagementCode}">Engagement Code: ENG-001</div>

                <!-- HSN Code Information -->
                <div th:if="${hsnCode}" th:text="${'HSN Code: ' + hsnCode}">HSN Code: 998313</div>
                <div th:if="${hsnDescription}" th:text="${'HSN Description: ' + hsnDescription}">HSN Description: IT consulting services</div>
            </div>

            <!-- SPOC Information Section -->
            <div th:if="${managerSpoc} or ${accountHeadSpoc} or ${businessHeadSpoc} or ${hrSpoc} or ${financeSpoc}" style="margin-top: 15px; border-top: 1px solid #ddd; padding-top: 10px;">
                <div style="font-weight: bold; margin-bottom: 5px;">Contact Information:</div>
                <!-- Manager SPOC -->
                <div th:if="${managerSpocName}">
                    <span style="font-weight: 500;">Manager:</span>
                    <span th:text="${managerSpocName}">John Doe</span>
                    <span th:if="${managerSpocEmail}" th:text="${' (' + managerSpocEmail + ')'}"> (<EMAIL>)</span>
                    <span th:if="${managerSpocPhone}" th:text="${' - ' + managerSpocPhone}"> - +91 **********</span>
                </div>

                <!-- Account Head SPOC -->
                <div th:if="${accountHeadSpocName}">
                    <span style="font-weight: 500;">Account Head:</span>
                    <span th:text="${accountHeadSpocName}">Jane Smith</span>
                    <span th:if="${accountHeadSpocEmail}" th:text="${' (' + accountHeadSpocEmail + ')'}"> (<EMAIL>)</span>
                    <span th:if="${accountHeadSpocPhone}" th:text="${' - ' + accountHeadSpocPhone}"> - +91 **********</span>
                </div>

                <!-- Business Head SPOC -->
                <div th:if="${businessHeadSpocName}">
                    <span style="font-weight: 500;">Business Head:</span>
                    <span th:text="${businessHeadSpocName}">Alex Johnson</span>
                    <span th:if="${businessHeadSpocEmail}" th:text="${' (' + businessHeadSpocEmail + ')'}"> (<EMAIL>)</span>
                    <span th:if="${businessHeadSpocPhone}" th:text="${' - ' + businessHeadSpocPhone}"> - +91 **********</span>
                </div>

                <!-- HR SPOC -->
                <div th:if="${hrSpocName}">
                    <span style="font-weight: 500;">HR:</span>
                    <span th:text="${hrSpocName}">Sarah Williams</span>
                    <span th:if="${hrSpocEmail}" th:text="${' (' + hrSpocEmail + ')'}"> (<EMAIL>)</span>
                    <span th:if="${hrSpocPhone}" th:text="${' - ' + hrSpocPhone}"> - +91 **********</span>
                </div>

                <!-- Finance SPOC -->
                <div th:if="${financeSpocName}">
                    <span style="font-weight: 500;">Finance:</span>
                    <span th:text="${financeSpocName}">Bob Johnson</span>
                    <span th:if="${financeSpocEmail}" th:text="${' (' + financeSpocEmail + ')'}"> (<EMAIL>)</span>
                    <span th:if="${financeSpocPhone}" th:text="${' - ' + financeSpocPhone}"> - +91 **********</span>
                </div>
            </div>
        </div>

        <div class="footer">
            Thank you for your business!
        </div>
    </div>
</body>
</html>
