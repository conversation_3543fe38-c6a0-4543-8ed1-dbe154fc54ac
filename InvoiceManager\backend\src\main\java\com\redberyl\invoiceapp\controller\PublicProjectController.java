package com.redberyl.invoiceapp.controller;

import com.redberyl.invoiceapp.dto.ProjectDto;
import com.redberyl.invoiceapp.exception.NoContentException;
import com.redberyl.invoiceapp.service.ProjectService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

/**
 * Public controller for projects that doesn't require authentication
 * This is useful for testing and development purposes
 */
@RestController
@RequestMapping("/projects")
@Tag(name = "Public Project API", description = "Public API for projects (no authentication required)")
@CrossOrigin(origins = {
    "http://localhost:5173",
    "http://127.0.0.1:5173",
    "http://************:5173",
    "http://localhost:3000",
    "http://localhost:3060"
}, allowedHeaders = "*", methods = {RequestMethod.GET, RequestMethod.POST, RequestMethod.PUT, RequestMethod.DELETE, RequestMethod.OPTIONS}, maxAge = 3600)
public class PublicProjectController {

    @Autowired
    private ProjectService projectService;

    @GetMapping
    @Operation(summary = "Get all projects (public)", description = "Get all projects without authentication")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Projects found"),
            @ApiResponse(responseCode = "204", description = "No projects found", content = @Content)
    })
    public ResponseEntity<List<ProjectDto>> getAllProjects() {
        try {
            List<ProjectDto> projects = projectService.getAllProjects();
            System.out.println("PublicProjectController: Returning " + projects.size() + " projects");
            return new ResponseEntity<>(projects, HttpStatus.OK);
        } catch (NoContentException e) {
            System.out.println("PublicProjectController: No projects found");
            return ResponseEntity.noContent().build();
        } catch (Exception e) {
            System.err.println("PublicProjectController: Error fetching projects: " + e.getMessage());
            e.printStackTrace();
            return new ResponseEntity<>(new ArrayList<>(), HttpStatus.OK);
        }
    }

    @GetMapping("/{id}")
    @Operation(summary = "Get project by ID (public)", description = "Get project by ID without authentication")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Project found"),
            @ApiResponse(responseCode = "404", description = "Project not found"),
            @ApiResponse(responseCode = "400", description = "Invalid ID supplied")
    })
    public ResponseEntity<ProjectDto> getProjectById(@PathVariable Long id) {
        try {
            ProjectDto project = projectService.getProjectById(id);
            System.out.println("PublicProjectController: Returning project with ID: " + id);
            return new ResponseEntity<>(project, HttpStatus.OK);
        } catch (Exception e) {
            System.err.println("PublicProjectController: Error fetching project with ID " + id + ": " + e.getMessage());
            return ResponseEntity.notFound().build();
        }
    }

    @PostMapping
    @Operation(summary = "Create project (public)", description = "Create project without authentication")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "201", description = "Project created successfully"),
            @ApiResponse(responseCode = "400", description = "Invalid input")
    })
    public ResponseEntity<ProjectDto> createProject(@Valid @RequestBody ProjectDto projectDto) {
        try {
            ProjectDto createdProject = projectService.createProject(projectDto);
            System.out.println("PublicProjectController: Created project with ID: " + createdProject.getId());
            return new ResponseEntity<>(createdProject, HttpStatus.CREATED);
        } catch (Exception e) {
            System.err.println("PublicProjectController: Error creating project: " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.badRequest().build();
        }
    }

    @PutMapping("/{id}")
    @Operation(summary = "Update project (public)", description = "Update project without authentication")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "200", description = "Project updated successfully"),
            @ApiResponse(responseCode = "404", description = "Project not found"),
            @ApiResponse(responseCode = "400", description = "Invalid input")
    })
    public ResponseEntity<ProjectDto> updateProject(@PathVariable Long id, @Valid @RequestBody ProjectDto projectDto) {
        try {
            ProjectDto updatedProject = projectService.updateProject(id, projectDto);
            System.out.println("PublicProjectController: Updated project with ID: " + id);
            return new ResponseEntity<>(updatedProject, HttpStatus.OK);
        } catch (Exception e) {
            System.err.println("PublicProjectController: Error updating project with ID " + id + ": " + e.getMessage());
            e.printStackTrace();
            return ResponseEntity.badRequest().build();
        }
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "Delete project (public)", description = "Delete project without authentication")
    @ApiResponses(value = {
            @ApiResponse(responseCode = "204", description = "Project deleted successfully"),
            @ApiResponse(responseCode = "404", description = "Project not found"),
            @ApiResponse(responseCode = "400", description = "Invalid ID supplied")
    })
    public ResponseEntity<Void> deleteProject(@PathVariable Long id) {
        try {
            projectService.deleteProject(id);
            System.out.println("PublicProjectController: Deleted project with ID: " + id);
            return ResponseEntity.noContent().build();
        } catch (Exception e) {
            System.err.println("PublicProjectController: Error deleting project with ID " + id + ": " + e.getMessage());
            return ResponseEntity.notFound().build();
        }
    }

    @GetMapping("/sample")
    @Operation(summary = "Get sample projects", description = "This endpoint no longer returns sample data")
    public ResponseEntity<List<ProjectDto>> getSampleProjects() {
        System.out.println("Sample projects endpoint called, returning empty list");
        // Return empty list instead of sample data
        return new ResponseEntity<>(new ArrayList<>(), HttpStatus.OK);
    }
}
