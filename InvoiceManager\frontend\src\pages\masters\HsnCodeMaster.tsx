import { useState, useEffect } from "react";
import { toast } from "sonner";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Label } from "@/components/ui/label";
import { Loader2, MoreHorizontal, Plus, Search } from "lucide-react";
import { HsnCode, postgresHsnCodeService } from "@/services/postgresHsnCodeService";
import { validateHsnCode, formatHsnCodeInput } from "@/utils/hsnCodeValidation";

// Empty array for HSN codes - we'll fetch from the database
const emptyHsnCodes: HsnCode[] = [];

const HsnCodeMaster = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);
  const [selectedHsnCode, setSelectedHsnCode] = useState<HsnCode | null>(null);
  const [formData, setFormData] = useState({
    code: "",
    description: "",
    gstRate: 0,
  });

  // State for HSN codes
  const [hsnCodes, setHsnCodes] = useState<HsnCode[]>([]);
  const [hsnCodesLoading, setHsnCodesLoading] = useState(true);
  const [hsnCodesError, setHsnCodesError] = useState<Error | null>(null);

  // Debug function to log current state
  const logCurrentState = () => {
    console.log("Current HSN Codes State:", {
      count: hsnCodes.length,
      codes: hsnCodes.map(code => ({ id: code.id, code: code.code })),
      loading: hsnCodesLoading,
      error: hsnCodesError?.message
    });
  };

  // Function to load HSN codes from PostgreSQL database
  const loadHsnCodes = async () => {
    setHsnCodesLoading(true);

    try {
      console.log("Fetching HSN codes from PostgreSQL database...");
      const data = await postgresHsnCodeService.getAllHsnCodes();
      console.log("Fetched HSN codes:", data);

      if (Array.isArray(data) && data.length > 0) {
        // Sort HSN codes by ID in descending order to show newest first
        const sortedData = [...data].sort((a, b) => {
          // If both have IDs, sort by ID in descending order
          if (a.id && b.id) {
            return b.id - a.id;
          }
          // If only one has an ID, prioritize the one with an ID
          if (a.id) return -1;
          if (b.id) return 1;
          // If neither has an ID, maintain original order
          return 0;
        });

        setHsnCodes(sortedData);
        console.log("Set sorted HSN codes:", sortedData);
      } else {
        console.log("No HSN codes found in database");
        // Use empty array if no data in database
        setHsnCodes(emptyHsnCodes);
      }

      setHsnCodesError(null);
    } catch (error) {
      console.error("Error loading HSN codes from database:", error);
      toast.error(`Failed to load HSN codes: ${error instanceof Error ? error.message : 'Database connection error'}`);
      setHsnCodesError(error instanceof Error ? error : new Error("Failed to load HSN codes from database"));

      // Try to fetch again with a different endpoint
      try {
        const response = await fetch('http://localhost:8091/api/hsn-codes');
        if (response.ok) {
          const data = await response.json();
          if (Array.isArray(data) && data.length > 0) {
            setHsnCodes(data);
            setHsnCodesError(null);
            return;
          }
        }
      } catch (fetchError) {
        console.error("Secondary fetch attempt failed:", fetchError);
      }

      // If all attempts fail, use empty array
      setHsnCodes(emptyHsnCodes);
    } finally {
      setHsnCodesLoading(false);
    }
  };

  // Function to refresh HSN codes
  const refetchHsnCodes = async () => {
    try {
      // Show loading toast
      const loadingToast = toast.loading("Refreshing HSN codes...");

      // Get current HSN codes to preserve them if fetch fails
      const currentHsnCodes = [...hsnCodes];

      // Set loading state
      setHsnCodesLoading(true);

      try {
        console.log("Manually refreshing HSN codes");
        let data;

        try {
          // Try using the service first
          data = await postgresHsnCodeService.getAllHsnCodes();
          console.log("Fetched HSN codes during manual refresh using service:", data);
        } catch (serviceError) {
          console.error("Error fetching HSN codes using service:", serviceError);

          // If service fails, try direct fetch
          try {
            // Get server URL from environment
            const serverUrl = import.meta.env.VITE_API_BASE_URL || 'http://192.168.1.30:8091';
            const response = await fetch(`${serverUrl}/api/hsn-codes`);
            if (response.ok) {
              data = await response.json();
              console.log("Fetched HSN codes during manual refresh using direct fetch:", data);
            } else {
              console.error("Direct fetch failed with status:", response.status);
            }
          } catch (directFetchError) {
            console.error("Direct fetch failed:", directFetchError);
          }
        }

        if (Array.isArray(data) && data.length > 0) {
          // Sort HSN codes by ID in descending order to show newest first
          const sortedData = [...data].sort((a, b) => {
            // If both have IDs, sort by ID in descending order
            if (a.id && b.id) {
              return b.id - a.id;
            }
            // If only one has an ID, prioritize the one with an ID
            if (a.id) return -1;
            if (b.id) return 1;
            // If neither has an ID, maintain original order
            return 0;
          });

          // Only update state if we have valid data
          setHsnCodes(sortedData);
          setHsnCodesError(null);
          console.log("Updated HSN codes after manual refresh:", sortedData);

          // Dismiss loading toast with success
          toast.dismiss(loadingToast);
          toast.success("HSN codes refreshed successfully");
        } else {
          console.warn("No HSN codes found during manual refresh, keeping current data");
          // Keep current data if no new data is found
          setHsnCodes(currentHsnCodes);

          // Dismiss loading toast with info
          toast.dismiss(loadingToast);
          toast.info("No HSN codes found on the server");
        }
      } catch (fetchError) {
        console.error("Error fetching HSN codes during manual refresh:", fetchError);

        // Keep current data if fetch fails
        setHsnCodes(currentHsnCodes);
        setHsnCodesError(fetchError instanceof Error ? fetchError : new Error("Failed to refresh HSN codes"));

        // Dismiss loading toast with error
        toast.dismiss(loadingToast);
        toast.error(`Failed to refresh HSN codes: ${fetchError instanceof Error ? fetchError.message : 'Server connection error'}`);
      } finally {
        setHsnCodesLoading(false);
      }
    } catch (error) {
      console.error("Error in refetchHsnCodes:", error);
      toast.error("Failed to refresh HSN codes. Please try again.");
    }
  };

  // Filter HSN codes based on search term
  const filteredHsnCodes = hsnCodes.filter(
    (hsnCode: HsnCode) =>
      hsnCode.code.toLowerCase().includes(searchTerm.toLowerCase()) ||
      hsnCode.description.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Handle form input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;

    // For HSN code, format input using utility function
    if (name === "code") {
      const formattedCode = formatHsnCodeInput(value);
      setFormData((prev) => ({
        ...prev,
        [name]: formattedCode,
      }));
    } else {
      setFormData((prev) => ({
        ...prev,
        [name]: name === "gstRate" ? parseFloat(value) || 0 : value,
      }));
    }
  };

  // Reset form data
  const resetForm = () => {
    setFormData({
      code: "",
      description: "",
      gstRate: 0,
    });
    setIsEditMode(false);
    setSelectedHsnCode(null);
  };

  // Open form for creating a new HSN code
  const handleAddNew = () => {
    resetForm();
    setIsFormOpen(true);
  };

  // Open form for editing an existing HSN code
  const handleEdit = (hsnCode: HsnCode) => {
    console.log("Editing HSN code:", hsnCode);

    // Validate that we have a valid HSN code with ID
    if (!hsnCode.id) {
      toast.error("Cannot edit HSN code: ID is missing");
      return;
    }

    // Make sure we have all fields from the HSN code
    setFormData({
      code: hsnCode.code || "",
      description: hsnCode.description || "",
      gstRate: typeof hsnCode.gstRate === 'number' ? hsnCode.gstRate : parseFloat(hsnCode.gstRate?.toString() || '0'),
    });

    setSelectedHsnCode(hsnCode);
    setIsEditMode(true);
    setIsFormOpen(true);

    console.log("Edit form opened with data:", {
      code: hsnCode.code,
      description: hsnCode.description,
      gstRate: hsnCode.gstRate,
      isEditMode: true
    });
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    console.log("Form submission started:", {
      isEditMode,
      selectedHsnCode: selectedHsnCode?.id,
      formData
    });

    // Validate form data
    if (!formData.code) {
      toast.error("HSN code is required");
      return;
    }

    // Validate HSN code format using utility function
    const validationResult = validateHsnCode(formData.code);
    if (!validationResult.isValid) {
      toast.error(validationResult.error || "Invalid HSN code format");
      return;
    }

    // Show loading toast
    const loadingToast = toast.loading(
      isEditMode ? "Updating HSN code..." : "Creating HSN code..."
    );

    try {
      if (isEditMode && selectedHsnCode) {
        // Validate that we have a valid ID for updating
        if (!selectedHsnCode.id) {
          throw new Error("Cannot update HSN code: ID is missing");
        }

        // Update existing HSN code in database
        console.log(`Updating HSN code with ID ${selectedHsnCode.id}:`, formData);

        const updatePayload = {
          id: selectedHsnCode.id,
          code: formData.code,
          description: formData.description,
          gstRate: formData.gstRate,
          createdAt: selectedHsnCode.createdAt,
          updatedAt: new Date().toISOString()
        };

        const updatedHsnCode = await postgresHsnCodeService.updateHsnCode(selectedHsnCode.id, updatePayload);

        console.log("HSN code updated successfully in database:", updatedHsnCode);

        // Update the HSN code in the local state to avoid a full reload
        setHsnCodes(prevCodes => {
          const updatedCodes = prevCodes.map(code =>
            code.id === selectedHsnCode.id ? updatedHsnCode : code
          );
          console.log(`Updated HSN code ${selectedHsnCode.id} in local state`);
          return updatedCodes;
        });

        console.log("HSN code update completed successfully");
      } else {
        // Create new HSN code in database
        console.log("Creating new HSN code:", formData);

        const newHsnCode = await postgresHsnCodeService.createHsnCode({
          code: formData.code,
          description: formData.description,
          gstRate: formData.gstRate
        });

        console.log("HSN code created successfully in database:", newHsnCode);

        // Add the new HSN code to the local state to avoid a full reload
        if (newHsnCode && newHsnCode.id) {
          setHsnCodes(prevCodes => [newHsnCode, ...prevCodes]);
        } else {
          // If the new HSN code doesn't have an ID, reload all data
          await loadHsnCodes();
        }
      }

      // Dismiss loading toast
      toast.dismiss(loadingToast);

      // Show success toast
      toast.success(
        isEditMode
          ? "HSN code updated successfully"
          : "HSN code created successfully"
      );

      // Close the form
      setIsFormOpen(false);
      resetForm();

      // No need to refresh - local state has been updated
    } catch (error) {
      console.error("Error saving HSN code:", error);

      // Dismiss loading toast
      toast.dismiss(loadingToast);

      // Show error toast
      toast.error(`Failed to save HSN code: ${error instanceof Error ? error.message : 'Database connection error'}`);
    }
  };

  // Handle HSN code deletion
  const handleDelete = async (hsnCode: HsnCode) => {
    if (!confirm(`Are you sure you want to delete HSN code ${hsnCode.code}?`)) {
      return;
    }

    // Validate HSN code has an ID
    if (!hsnCode.id) {
      toast.error("Cannot delete HSN code: ID is missing");
      return;
    }

    // Show loading toast
    const loadingToast = toast.loading("Deleting HSN code...");

    try {
      console.log(`Deleting HSN code with ID ${hsnCode.id} and code ${hsnCode.code}`);

      // Delete HSN code from database
      await postgresHsnCodeService.deleteHsnCode(hsnCode.id);

      console.log("HSN code deleted successfully from database");

      // Update the local state to remove the deleted HSN code immediately
      setHsnCodes(prevCodes => {
        const updatedCodes = prevCodes.filter(code => code.id !== hsnCode.id);
        console.log(`Removed HSN code ${hsnCode.id} from local state. Remaining codes:`, updatedCodes.length);
        return updatedCodes;
      });

      // Dismiss loading toast
      toast.dismiss(loadingToast);

      // Show success toast
      toast.success(`HSN code ${hsnCode.code} deleted successfully`);

    } catch (error) {
      console.error("Error deleting HSN code:", error);

      // Dismiss loading toast
      toast.dismiss(loadingToast);

      // Show error toast with more specific message
      const errorMessage = error instanceof Error ? error.message : "Unknown error";
      toast.error(`Failed to delete HSN code ${hsnCode.code}: ${errorMessage}`);

      // If deletion failed, refresh the data to ensure UI consistency
      console.log("Refreshing HSN codes due to deletion error");
      try {
        await loadHsnCodes();
      } catch (refreshError) {
        console.error("Failed to refresh HSN codes after deletion error:", refreshError);
        toast.error("Failed to refresh data. Please reload the page.");
      }
    }
  };

  // Debug effect to log state changes
  useEffect(() => {
    logCurrentState();
  }, [hsnCodes]);

  // Load HSN codes on component mount
  useEffect(() => {
    console.log("Loading HSN codes on component mount");
    let isMounted = true;

    // Use an IIFE to handle the async function
    (async () => {
      try {
        if (isMounted) {
          console.log("Initial load of HSN codes");
          await loadHsnCodes();

          // Force a refresh after a short delay to ensure we get the latest data
          setTimeout(async () => {
            if (isMounted) {
              await refetchHsnCodes();
            }
          }, 500);
        }
      } catch (error) {
        console.error("Error in useEffect when loading HSN codes:", error);
      }
    })();

    // Cleanup function to prevent state updates after unmount
    return () => {
      isMounted = false;
    };
  }, []); // Empty dependency array to run only once on mount

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">HSN Codes Master</h1>
        <Button onClick={handleAddNew}>
          <Plus className="h-4 w-4 mr-2" />
          Add New HSN Code
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>HSN Codes</CardTitle>
          <CardDescription>
            Manage Harmonized System of Nomenclature (HSN) codes for your products and services.
          </CardDescription>
          <div className="flex items-center gap-2">
            <div className="relative flex-1">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search HSN codes..."
                className="pl-8"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={async () => {
                try {
                  await refetchHsnCodes();
                } catch (error) {
                  console.error("Error refreshing HSN codes:", error);
                }
              }}
              disabled={hsnCodesLoading}
            >
              {hsnCodesLoading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                "Refresh"
              )}
            </Button>
            {process.env.NODE_ENV === 'development' && (
              <Button onClick={logCurrentState} variant="outline" size="sm">
                Debug State
              </Button>
            )}
          </div>
        </CardHeader>
        <CardContent>
          {hsnCodesLoading ? (
            <div className="flex justify-center items-center py-8">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
            </div>
          ) : hsnCodesError ? (
            <div className="text-center py-8 text-red-500">
              Error loading HSN codes. Please try again.
            </div>
          ) : filteredHsnCodes.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              No HSN codes found. Add your first HSN code!
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>HSN Code</TableHead>
                  <TableHead>Description</TableHead>
                  <TableHead>GST Rate (%)</TableHead>
                  <TableHead className="w-[100px]">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredHsnCodes.map((hsnCode: HsnCode) => (
                  <TableRow key={hsnCode.id}>
                    <TableCell className="font-medium">{hsnCode.code}</TableCell>
                    <TableCell>{hsnCode.description}</TableCell>
                    <TableCell>{hsnCode.gstRate}%</TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <MoreHorizontal className="h-4 w-4" />
                            <span className="sr-only">Actions</span>
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={() => handleEdit(hsnCode)}>
                            Edit
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            className="text-red-500"
                            onClick={() => handleDelete(hsnCode)}
                          >
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>

      {/* HSN Code Form Dialog */}
      <Dialog open={isFormOpen} onOpenChange={setIsFormOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {isEditMode ? "Edit HSN Code" : "Add New HSN Code"}
            </DialogTitle>
            <DialogDescription>
              {isEditMode
                ? "Update the details of the HSN code."
                : "Enter the details of the new HSN code."}
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleSubmit}>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="code" className="text-right">
                  HSN Code <span className="text-red-500">*</span>
                </Label>
                <div className="col-span-3">
                  <Input
                    id="code"
                    name="code"
                    value={formData.code}
                    onChange={handleInputChange}
                    placeholder="Enter 6-digit HSN code"
                    pattern="[0-9]{6}"
                    maxLength={6}
                    minLength={6}
                    title="HSN code must be exactly 6 digits (0-9)"
                    required
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    Must be exactly 6 digits (e.g., 998313)
                  </p>
                </div>
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="description" className="text-right">
                  Description
                </Label>
                <Input
                  id="description"
                  name="description"
                  value={formData.description}
                  onChange={handleInputChange}
                  className="col-span-3"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="gstRate" className="text-right">
                  GST Rate (%)
                </Label>
                <Input
                  id="gstRate"
                  name="gstRate"
                  type="number"
                  step="0.01"
                  min="0"
                  max="100"
                  value={formData.gstRate}
                  onChange={handleInputChange}
                  className="col-span-3"
                />
              </div>
            </div>
            <DialogFooter>
              <Button type="button" variant="outline" onClick={() => setIsFormOpen(false)}>
                Cancel
              </Button>
              <Button type="submit">
                {isEditMode ? "Update HSN Code" : "Add HSN Code"}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default HsnCodeMaster;
