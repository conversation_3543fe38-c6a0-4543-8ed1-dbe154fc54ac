const getApiBaseUrl = () => import.meta.env.VITE_getApiBaseUrl() || 'http://************:8091';

export interface GstChallanEntry {
  candidateName: string;  // Changed from clientName to candidateName
  clientAddress: string;
  clientGstNo: string;
  invoiceNo: string;
  invoiceDate: string;
  invoiceMonth: string;
  billAmount: number;
  cgst: number;
  sgst: number;
  igst: number;
  invoiceAmount: number;
}

export interface GstChallan {
  challanNumber: string;
  challanDate: string;
  month: string;
  entries: GstChallanEntry[];
  totalBillAmount: number;
  totalCgst: number;
  totalSgst: number;
  totalIgst: number;
  totalInvoiceAmount: number;
}

export const gstChallanService = {
  /**
   * Generate GST Challan data for a specific month
   */
  async generateGstChallanData(month: string): Promise<GstChallan> {
    const response = await fetch(`${getApiBaseUrl()}/api/gst-challan/data?month=${encodeURIComponent(month)}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to generate GST Challan data: ${response.status}`);
    }

    return response.json();
  },

  /**
   * Generate GST Challan data for a date range
   */
  async generateGstChallanDataForDateRange(startDate: string, endDate: string): Promise<GstChallan> {
    const response = await fetch(`\$\{getApiBaseUrl\(\)\}/api/gst-challan/data/date-range?startDate=${startDate}&endDate=${endDate}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to generate GST Challan data for date range: ${response.status}`);
    }

    return response.json();
  },

  /**
   * Generate and download GST Challan PDF for a specific month
   */
  async downloadGstChallanPdf(month: string): Promise<void> {
    const response = await fetch(`\$\{getApiBaseUrl\(\)\}/api/gst-challan/pdf?month=${encodeURIComponent(month)}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to generate GST Challan PDF: ${response.status}`);
    }

    // Get the PDF blob
    const blob = await response.blob();
    
    // Create download link
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `GST_Challan_${month.replace(' ', '_')}.pdf`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  },

  /**
   * Generate and download GST Challan PDF for a date range
   */
  async downloadGstChallanPdfForDateRange(startDate: string, endDate: string): Promise<void> {
    const response = await fetch(`\$\{getApiBaseUrl\(\)\}/api/gst-challan/pdf/date-range?startDate=${startDate}&endDate=${endDate}`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to generate GST Challan PDF for date range: ${response.status}`);
    }

    // Get the PDF blob
    const blob = await response.blob();

    // Create download link
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `GST_Challan_${startDate}_to_${endDate}.pdf`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  },

  /**
   * Generate and download GST Challan PDF for specific invoice IDs
   */
  async downloadGstChallanPdfForInvoices(invoiceIds: number[]): Promise<void> {
    console.log("=== GST Challan Service Debug ===");
    console.log("getApiBaseUrl():", getApiBaseUrl());
    console.log("Invoice IDs to send:", invoiceIds);
    console.log("Request URL:", `\$\{getApiBaseUrl\(\)\}/api/gst-challan/pdf/invoices`);

    try {
      const response = await fetch(`\$\{getApiBaseUrl\(\)\}/api/gst-challan/pdf/invoices`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(invoiceIds),
      });

      console.log("Response status:", response.status);
      console.log("Response headers:", response.headers);

      if (!response.ok) {
        const errorText = await response.text();
        console.error("Error response body:", errorText);
        throw new Error(`Failed to generate GST Challan PDF for selected invoices: ${response.status} - ${errorText}`);
      }

      // Get the PDF blob
      const blob = await response.blob();
      console.log("PDF blob size:", blob.size);

      if (blob.size === 0) {
        throw new Error("Received empty PDF file");
      }

      // Create download link
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `GST_Challan_Selected_Invoices.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      console.log("PDF download completed successfully");
    } catch (error) {
      console.error("=== GST Challan Service Error ===");
      console.error("Error in downloadGstChallanPdfForInvoices:", error);
      throw error;
    }
  },

  /**
   * Generate and download GST Challan PDF from provided data
   */
  async downloadGstChallanPdfFromData(gstChallanData: GstChallan): Promise<void> {
    const response = await fetch(`\$\{getApiBaseUrl\(\)\}/api/gst-challan/pdf`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(gstChallanData),
    });

    if (!response.ok) {
      throw new Error(`Failed to generate GST Challan PDF from data: ${response.status}`);
    }

    // Get the PDF blob
    const blob = await response.blob();
    
    // Create download link
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `GST_Challan_${gstChallanData.challanNumber}.pdf`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  },
};
