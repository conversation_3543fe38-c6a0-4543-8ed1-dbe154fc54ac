/**
 * Invoice Template Configuration Service
 * 
 * Service for managing dynamic invoice template configurations
 */

export interface InvoiceTemplateConfig {
  id?: number;
  configKey: string;
  configValue: string;
  configType: string; // TEXT, NUMBER, BOOLEAN, JSON
  category: string; // COMPANY, BANK, TEMPLATE, etc.
  description: string;
  isActive: boolean;
  displayOrder: number;
}

const getApiBaseUrl = () => `${import.meta.env.VITE_getApiBaseUrl() || 'http://************:8091'}/api/invoice-template-config`;

/**
 * Invoice Template Configuration Service
 */
export const invoiceTemplateConfigService = {
  /**
   * Get all configurations
   */
  getAllConfigurations: async (): Promise<InvoiceTemplateConfig[]> => {
    try {
      const response = await fetch(`${getApiBaseUrl()}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching configurations:', error);
      throw error;
    }
  },

  /**
   * Get configuration by ID
   */
  getConfigurationById: async (id: number): Promise<InvoiceTemplateConfig> => {
    try {
      const response = await fetch(`${getApiBaseUrl()}/${id}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching configuration:', error);
      throw error;
    }
  },

  /**
   * Get configurations by category
   */
  getConfigurationsByCategory: async (category: string): Promise<InvoiceTemplateConfig[]> => {
    try {
      const response = await fetch(`${getApiBaseUrl()}/category/${category}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching configurations by category:', error);
      throw error;
    }
  },

  /**
   * Get all configuration values as key-value pairs
   */
  getAllConfigValues: async (): Promise<Record<string, string>> => {
    try {
      const response = await fetch(`${getApiBaseUrl()}/values`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching configuration values:', error);
      throw error;
    }
  },

  /**
   * Get configuration values by category
   */
  getConfigValuesByCategory: async (category: string): Promise<Record<string, string>> => {
    try {
      const response = await fetch(`${getApiBaseUrl()}/values/category/${category}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error fetching configuration values by category:', error);
      throw error;
    }
  },

  /**
   * Create new configuration
   */
  createConfiguration: async (config: Omit<InvoiceTemplateConfig, 'id'>): Promise<InvoiceTemplateConfig> => {
    try {
      const response = await fetch(`${getApiBaseUrl()}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(config),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error creating configuration:', error);
      throw error;
    }
  },

  /**
   * Update existing configuration
   */
  updateConfiguration: async (id: number, config: Partial<InvoiceTemplateConfig>): Promise<InvoiceTemplateConfig> => {
    try {
      const response = await fetch(`${getApiBaseUrl()}/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(config),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error updating configuration:', error);
      throw error;
    }
  },

  /**
   * Delete configuration
   */
  deleteConfiguration: async (id: number): Promise<void> => {
    try {
      const response = await fetch(`${getApiBaseUrl()}/${id}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
    } catch (error) {
      console.error('Error deleting configuration:', error);
      throw error;
    }
  },

  /**
   * Bulk update configurations
   */
  bulkUpdateConfigurations: async (configs: InvoiceTemplateConfig[]): Promise<InvoiceTemplateConfig[]> => {
    try {
      const response = await fetch(`${getApiBaseUrl()}/bulk`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(configs),
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Error bulk updating configurations:', error);
      throw error;
    }
  },

  /**
   * Initialize default configurations
   */
  initializeDefaultConfigurations: async (): Promise<string> => {
    try {
      const response = await fetch(`${getApiBaseUrl()}/initialize`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      return await response.text();
    } catch (error) {
      console.error('Error initializing default configurations:', error);
      throw error;
    }
  },
};
