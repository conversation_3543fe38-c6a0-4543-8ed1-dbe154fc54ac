-- Migration script to update existing HSN codes to 6-digit format
-- This script will pad existing HSN codes with leading zeros if they are less than 6 digits

-- Update HSN codes that are less than 6 digits by padding with leading zeros
UPDATE hsn_codes 
SET code = LPAD(code, 6, '0') 
WHERE LENGTH(code) < 6 AND code ~ '^[0-9]+$';

-- Add constraint to ensure all HSN codes are exactly 6 digits
ALTER TABLE hsn_codes 
ADD CONSTRAINT chk_hsn_code_format 
CHECK (code ~ '^[0-9]{6}$');

-- Update the column length constraint
ALTER TABLE hsn_codes 
ALTER COLUMN code TYPE VARCHAR(6);

-- Add comment to the table
COMMENT ON TABLE hsn_codes IS 'HSN (Harmonized System of Nomenclature) codes for tax classification';
COMMENT ON COLUMN hsn_codes.code IS 'HSN code must be exactly 6 digits (0-9)';
