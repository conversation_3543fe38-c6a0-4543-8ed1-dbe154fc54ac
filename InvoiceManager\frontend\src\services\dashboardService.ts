import { api } from "@/lib/axios";

export interface DashboardMetrics {
  totalInvoices: {
    count: number;
    trend: number;
    pendingApproval: number;
  };
  totalPayments: {
    amount: number;
    trend: number;
    pendingPayments: number;
  };
  activeClients: {
    count: number;
    trend: number;
    newThisMonth: number;
  };
  documents: {
    count: number;
    trend: number;
    awaitingApproval: number;
  };
}

export const dashboardService = {
  /**
   * Fetch dashboard metrics from the API
   */
  getDashboardMetrics: async (): Promise<DashboardMetrics> => {
    try {
      console.log('Fetching dashboard metrics from API...');

      // Get server URL from environment
      const serverUrl = import.meta.env.VITE_API_BASE_URL || 'http://************:8091';

      // Try multiple endpoints to ensure we get the data
      const endpoints = [
        `${serverUrl}/api/dashboard/public/metrics`,
        `${serverUrl}/api/dashboard/metrics`,
        '/api/dashboard/metrics'
      ];

      let lastError = null;

      for (const endpoint of endpoints) {
        try {
          console.log(`Trying endpoint: ${endpoint}`);
          const response = await fetch(endpoint, {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json',
              'Accept': 'application/json'
            }
          });

          if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
          }

          const data = await response.json();
          console.log('✓ Dashboard metrics fetched successfully:', data);
          return data;
        } catch (error) {
          console.warn(`Failed to fetch from ${endpoint}:`, error);
          lastError = error;
          continue;
        }
      }

      throw lastError || new Error('All endpoints failed');
    } catch (error) {
      console.error('❌ Error fetching dashboard metrics:', error);

      // Instead of returning fallback data, throw the error so the UI can handle it
      throw new Error(`Failed to fetch dashboard metrics: ${error.message}`);
    }
  }
};
