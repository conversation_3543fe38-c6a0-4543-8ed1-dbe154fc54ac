# Environment Setup Guide

This guide explains how to configure the frontend to connect to the backend API on different systems.

## Quick Setup

### Option 1: Automatic IP Detection (Recommended)
```bash
npm run setup-env
```
This will automatically detect your local IP address and configure the environment.

### Option 2: Use Localhost
```bash
npm run setup-env:localhost
```
Use this when both frontend and backend are running on the same machine.

### Option 3: Manual IP Configuration
```bash
node setup-env.js --ip=*************
```
Replace `*************` with your backend server's IP address.

## Manual Configuration

### 1. Copy the Example File
```bash
cp .env.example .env
```

### 2. Edit the .env File
Open `.env` and set the `VITE_API_URL`:

```bash
# For specific IP address
VITE_API_URL=http://************:8091/api

# For localhost development
VITE_API_URL=http://localhost:8091/api

# For dynamic hostname detection (leave empty)
VITE_API_URL=
```

## Environment Files

- `.env` - Main environment file (auto-generated or manually configured)
- `.env.local` - Local development (localhost)
- `.env.production` - Production (dynamic hostname)
- `.env.example` - Template file with examples

## Troubleshooting

### Issue: "localhost" errors on other systems
**Solution**: Run `npm run setup-env` to auto-detect the correct IP.

### Issue: Cannot connect to backend
**Solutions**:
1. Ensure backend is running on port 8091
2. Check firewall settings
3. Verify the IP address in `.env` file
4. Try: `npm run setup-env:localhost` if on same machine

### Issue: CORS errors
**Solution**: The backend should be configured to accept requests from your frontend IP.

## Network Requirements

- Frontend runs on port 3060
- Backend runs on port 8091
- Both should be accessible on the same network
- Firewall should allow connections on these ports

## Development vs Production

### Development
- Use `npm run setup-env:localhost` for local development
- Both frontend and backend on same machine

### Production/Network Deployment
- Use `npm run setup-env` for automatic IP detection
- Or manually set specific IP in `.env` file
- Ensure network connectivity between frontend and backend hosts
