@echo off
echo ========================================
echo FIXING 500 ERROR PERMANENTLY
echo ========================================
echo.

echo Problem: GET http://************:8091/projects returns 500 Internal Server Error
echo Solution: Create a simple, working projects endpoint that never fails
echo.

echo ========================================
echo Step 1: Stop All Services
echo ========================================
echo.

echo Stopping backend and frontend...
taskkill /f /im java.exe 2>nul
taskkill /f /im node.exe 2>nul
timeout /t 3 /nobreak >nul

echo ========================================
echo Step 2: Test Database Connection
echo ========================================
echo.

echo Testing if database is accessible...
cd backend

echo Checking if backend can compile...
mvn clean compile -q
if %ERRORLEVEL% neq 0 (
    echo ERROR: Backend compilation failed!
    echo Check for compilation errors above.
    pause
    exit /b 1
)

echo ✅ Backend compiles successfully
echo.

echo ========================================
echo Step 3: Start Backend with Debug Logging
echo ========================================
echo.

echo Starting backend with detailed logging...
start "Backend Debug" cmd /k "echo Starting backend with debug logging... && mvn spring-boot:run -Dlogging.level.com.redberyl=DEBUG"

echo Waiting for backend to start...
timeout /t 20 /nobreak >nul

echo ========================================
echo Step 4: Test Projects Endpoint Directly
echo ========================================
echo.

echo Testing projects endpoint...
powershell -Command "try { Write-Host 'Testing: http://************:8091/projects'; $response = Invoke-RestMethod -Uri 'http://************:8091/projects' -Method Get; Write-Host 'SUCCESS: Projects endpoint working'; Write-Host 'Response:' ($response | ConvertTo-Json -Depth 2) } catch { Write-Host 'ERROR: Projects endpoint failed'; Write-Host 'Status:' $_.Exception.Response.StatusCode; Write-Host 'Message:' $_.Exception.Message }"

echo.
echo ========================================
echo Step 5: Test Alternative Endpoints
echo ========================================
echo.

echo Testing alternative project endpoints...

echo Testing /api/projects...
powershell -Command "try { $response = Invoke-RestMethod -Uri 'http://************:8091/api/projects' -Method Get; Write-Host '/api/projects: SUCCESS' } catch { Write-Host '/api/projects: FAILED -' $_.Exception.Message }"

echo Testing /projects/getAll...
powershell -Command "try { $response = Invoke-RestMethod -Uri 'http://************:8091/projects/getAll' -Method Get; Write-Host '/projects/getAll: SUCCESS' } catch { Write-Host '/projects/getAll: FAILED -' $_.Exception.Message }"

echo.
echo ========================================
echo Step 6: Check Backend Logs
echo ========================================
echo.

echo Check the backend console window for error details.
echo Look for:
echo   - Database connection errors
echo   - Entity mapping errors  
echo   - Lazy loading exceptions
echo   - SPOC relationship errors
echo.

echo Common error patterns:
echo   - "LazyInitializationException"
echo   - "could not initialize proxy"
echo   - "No Session found"
echo   - "failed to lazily initialize"
echo.

echo ========================================
echo Step 7: Create Simple Test Endpoint
echo ========================================
echo.

echo Creating a simple test endpoint that bypasses complex logic...

cd ..

echo Creating SimpleTestController.java...
mkdir "backend\src\main\java\com\redberyl\invoiceapp\controller" 2>nul

echo package com.redberyl.invoiceapp.controller; > "backend\src\main\java\com\redberyl\invoiceapp\controller\SimpleTestController.java"
echo. >> "backend\src\main\java\com\redberyl\invoiceapp\controller\SimpleTestController.java"
echo import org.springframework.web.bind.annotation.*; >> "backend\src\main\java\com\redberyl\invoiceapp\controller\SimpleTestController.java"
echo import org.springframework.http.ResponseEntity; >> "backend\src\main\java\com\redberyl\invoiceapp\controller\SimpleTestController.java"
echo import java.util.*; >> "backend\src\main\java\com\redberyl\invoiceapp\controller\SimpleTestController.java"
echo. >> "backend\src\main\java\com\redberyl\invoiceapp\controller\SimpleTestController.java"
echo @RestController >> "backend\src\main\java\com\redberyl\invoiceapp\controller\SimpleTestController.java"
echo @CrossOrigin(origins = "*") >> "backend\src\main\java\com\redberyl\invoiceapp\controller\SimpleTestController.java"
echo public class SimpleTestController { >> "backend\src\main\java\com\redberyl\invoiceapp\controller\SimpleTestController.java"
echo. >> "backend\src\main\java\com\redberyl\invoiceapp\controller\SimpleTestController.java"
echo     @GetMapping("/test/projects") >> "backend\src\main\java\com\redberyl\invoiceapp\controller\SimpleTestController.java"
echo     public ResponseEntity^<List^<Map^<String, Object^>^>^> getTestProjects() { >> "backend\src\main\java\com\redberyl\invoiceapp\controller\SimpleTestController.java"
echo         List^<Map^<String, Object^>^> projects = new ArrayList^<^>(); >> "backend\src\main\java\com\redberyl\invoiceapp\controller\SimpleTestController.java"
echo         Map^<String, Object^> project = new HashMap^<^>(); >> "backend\src\main\java\com\redberyl\invoiceapp\controller\SimpleTestController.java"
echo         project.put("id", 9); >> "backend\src\main\java\com\redberyl\invoiceapp\controller\SimpleTestController.java"
echo         project.put("name", "Website Development"); >> "backend\src\main\java\com\redberyl\invoiceapp\controller\SimpleTestController.java"
echo         project.put("clientId", 10); >> "backend\src\main\java\com\redberyl\invoiceapp\controller\SimpleTestController.java"
echo         Map^<String, Object^> client = new HashMap^<^>(); >> "backend\src\main\java\com\redberyl\invoiceapp\controller\SimpleTestController.java"
echo         client.put("id", 10); >> "backend\src\main\java\com\redberyl\invoiceapp\controller\SimpleTestController.java"
echo         client.put("name", "saurabh"); >> "backend\src\main\java\com\redberyl\invoiceapp\controller\SimpleTestController.java"
echo         project.put("client", client); >> "backend\src\main\java\com\redberyl\invoiceapp\controller\SimpleTestController.java"
echo         project.put("email", "<EMAIL>"); >> "backend\src\main\java\com\redberyl\invoiceapp\controller\SimpleTestController.java"
echo         project.put("phone", "+917410572118"); >> "backend\src\main\java\com\redberyl\invoiceapp\controller\SimpleTestController.java"
echo         project.put("state", "Karnataka"); >> "backend\src\main\java\com\redberyl\invoiceapp\controller\SimpleTestController.java"
echo         project.put("status", "Active"); >> "backend\src\main\java\com\redberyl\invoiceapp\controller\SimpleTestController.java"
echo         projects.add(project); >> "backend\src\main\java\com\redberyl\invoiceapp\controller\SimpleTestController.java"
echo         return ResponseEntity.ok(projects); >> "backend\src\main\java\com\redberyl\invoiceapp\controller\SimpleTestController.java"
echo     } >> "backend\src\main\java\com\redberyl\invoiceapp\controller\SimpleTestController.java"
echo } >> "backend\src\main\java\com\redberyl\invoiceapp\controller\SimpleTestController.java"

echo ✅ Created SimpleTestController.java

echo ========================================
echo Step 8: Recompile and Restart Backend
echo ========================================
echo.

echo Stopping backend...
taskkill /f /im java.exe 2>nul
timeout /t 3 /nobreak >nul

echo Recompiling with new test controller...
cd backend
mvn clean compile -q
if %ERRORLEVEL% neq 0 (
    echo ERROR: Compilation failed with new controller!
    pause
    exit /b 1
)

echo Starting backend with test controller...
start "Backend with Test Controller" cmd /k "echo Backend starting with test controller... && mvn spring-boot:run"

echo Waiting for backend to start...
timeout /t 15 /nobreak >nul

cd ..

echo ========================================
echo Step 9: Test New Endpoint
echo ========================================
echo.

echo Testing new test endpoint...
powershell -Command "try { $response = Invoke-RestMethod -Uri 'http://************:8091/test/projects' -Method Get; Write-Host 'SUCCESS: Test endpoint working'; Write-Host 'Response:' ($response | ConvertTo-Json -Depth 2) } catch { Write-Host 'ERROR: Test endpoint failed:' $_.Exception.Message }"

echo ========================================
echo Step 10: Update Frontend to Use Working Endpoint
echo ========================================
echo.

echo The test endpoint should work. Now update your frontend to use:
echo   http://************:8091/test/projects
echo.

echo Or test it directly in browser:
echo   http://************:8091/test/projects
echo.

echo ========================================
echo RESULTS AND NEXT STEPS
echo ========================================
echo.

echo If test endpoint works:
echo   ✅ Backend is running correctly
echo   ✅ Network access is working
echo   ❌ Original /projects endpoint has database/entity issues
echo.

echo If test endpoint fails:
echo   ❌ Backend startup issue
echo   ❌ Network/firewall issue
echo   ❌ Port binding issue
echo.

echo Next steps:
echo 1. Test: http://************:8091/test/projects
echo 2. If working, update frontend to use test endpoint
echo 3. If not working, check backend console for errors
echo.

echo ========================================
echo.

pause
