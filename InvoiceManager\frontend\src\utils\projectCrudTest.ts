/**
 * Comprehensive test utility for Project CRUD operations
 * This file contains functions to test Create, Read, Update, Delete operations for projects
 */

import { projectService } from '@/services/projectService';
import { toast } from 'sonner';

export interface TestProject {
  name: string;
  clientId: number;
  description?: string;
  email?: string;
  phone?: string;
  gstNumber?: string;
  billingAddress?: string;
  shippingAddress?: string;
  state?: string;
  engagementCode?: string;
  clientPartnerName?: string;
  clientPartnerEmail?: string;
  clientPartnerPhone?: string;
  bdm?: string;
  commissionPercentage?: string;
  commissionAmount?: string;
  hsnCodeId?: number;
  startDate?: string;
  endDate?: string;
  status?: string;
  value?: string;
}

/**
 * Test project creation
 */
export const testCreateProject = async (): Promise<any> => {
  const testProject: TestProject = {
    name: `Test Project ${Date.now()}`,
    clientId: 1, // Assuming client with ID 1 exists
    description: 'Test project for CRUD operations',
    email: '<EMAIL>',
    phone: '+1234567890',
    gstNumber: 'GST123456789',
    billingAddress: '123 Test Street, Test City',
    shippingAddress: '123 Test Street, Test City',
    state: 'Test State',
    engagementCode: 'ENG001',
    clientPartnerName: 'Test Partner',
    clientPartnerEmail: '<EMAIL>',
    clientPartnerPhone: '+0987654321',
    bdm: 'Test BDM',
    commissionPercentage: '10',
    commissionAmount: '1000',
    startDate: '2024-01-01',
    endDate: '2024-12-31',
    status: 'Active',
    value: '10000'
  };

  try {
    console.log('Testing project creation with data:', testProject);
    const createdProject = await projectService.createProject(testProject);
    console.log('Project created successfully:', createdProject);
    toast.success(`Test project created with ID: ${createdProject.id}`);
    return createdProject;
  } catch (error) {
    console.error('Error creating test project:', error);
    toast.error(`Failed to create test project: ${error instanceof Error ? error.message : 'Unknown error'}`);
    throw error;
  }
};

/**
 * Test project reading/fetching
 */
export const testReadProjects = async (): Promise<any[]> => {
  try {
    console.log('Testing project reading...');
    const projects = await projectService.getAllProjects();
    console.log('Projects fetched successfully:', projects);
    toast.success(`Fetched ${projects.length} projects`);
    return projects;
  } catch (error) {
    console.error('Error reading projects:', error);
    toast.error(`Failed to read projects: ${error instanceof Error ? error.message : 'Unknown error'}`);
    throw error;
  }
};

/**
 * Test project update
 */
export const testUpdateProject = async (projectId: string | number, updates: Partial<TestProject>): Promise<any> => {
  try {
    console.log(`Testing project update for ID: ${projectId} with updates:`, updates);
    const updatedProject = await projectService.updateProject(projectId, updates);
    console.log('Project updated successfully:', updatedProject);
    toast.success(`Project ${projectId} updated successfully`);
    return updatedProject;
  } catch (error) {
    console.error('Error updating project:', error);
    toast.error(`Failed to update project: ${error instanceof Error ? error.message : 'Unknown error'}`);
    throw error;
  }
};

/**
 * Test project deletion
 */
export const testDeleteProject = async (projectId: string | number): Promise<void> => {
  try {
    console.log(`Testing project deletion for ID: ${projectId}`);
    await projectService.deleteProject(projectId);
    console.log('Project deleted successfully');
    toast.success(`Project ${projectId} deleted successfully`);
  } catch (error) {
    console.error('Error deleting project:', error);
    toast.error(`Failed to delete project: ${error instanceof Error ? error.message : 'Unknown error'}`);
    throw error;
  }
};

/**
 * Run comprehensive CRUD test
 */
export const runComprehensiveCrudTest = async (): Promise<void> => {
  const loadingToast = toast.loading('Running comprehensive CRUD test...');
  
  try {
    // Step 1: Test Create
    console.log('=== STEP 1: Testing CREATE ===');
    const createdProject = await testCreateProject();
    
    // Step 2: Test Read
    console.log('=== STEP 2: Testing READ ===');
    await testReadProjects();
    
    // Step 3: Test Update
    console.log('=== STEP 3: Testing UPDATE ===');
    const updates = {
      name: `Updated Test Project ${Date.now()}`,
      description: 'Updated description for test project',
      value: '15000'
    };
    await testUpdateProject(createdProject.id, updates);
    
    // Step 4: Test Delete
    console.log('=== STEP 4: Testing DELETE ===');
    await testDeleteProject(createdProject.id);
    
    toast.success('All CRUD operations completed successfully!', { id: loadingToast });
    console.log('=== CRUD TEST COMPLETED SUCCESSFULLY ===');
    
  } catch (error) {
    console.error('CRUD test failed:', error);
    toast.error(`CRUD test failed: ${error instanceof Error ? error.message : 'Unknown error'}`, { id: loadingToast });
    throw error;
  }
};

/**
 * Test specific project by ID
 */
export const testGetProjectById = async (projectId: string | number): Promise<any> => {
  try {
    console.log(`Testing get project by ID: ${projectId}`);
    const project = await projectService.getProjectById(projectId);
    console.log('Project fetched by ID:', project);
    toast.success(`Project ${projectId} fetched successfully`);
    return project;
  } catch (error) {
    console.error('Error getting project by ID:', error);
    toast.error(`Failed to get project by ID: ${error instanceof Error ? error.message : 'Unknown error'}`);
    throw error;
  }
};

// Export all test functions for use in components
export const projectCrudTests = {
  create: testCreateProject,
  read: testReadProjects,
  update: testUpdateProject,
  delete: testDeleteProject,
  getById: testGetProjectById,
  runAll: runComprehensiveCrudTest
};
