@echo off
setlocal enabledelayedexpansion

echo ========================================
echo Invoice Manager - Network Startup
echo ========================================
echo.

:: Check if we're in the right directory
if not exist "backend" (
    echo Error: backend directory not found!
    echo Please run this script from the InvoiceManager directory.
    pause
    exit /b 1
)

if not exist "frontend" (
    echo Error: frontend directory not found!
    echo Please run this script from the InvoiceManager directory.
    pause
    exit /b 1
)

:: Configure network settings
echo Configuring network settings...
call scripts\configure-network.bat
echo.

:: Check if Java is installed
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Java is not installed or not in PATH!
    echo Please install Java 17 or later.
    pause
    exit /b 1
)

:: Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Error: Node.js is not installed or not in PATH!
    echo Please install Node.js.
    pause
    exit /b 1
)

echo Starting Invoice Manager...
echo.

:: Start backend in a new window
echo Starting backend server...
start "Invoice Manager Backend" cmd /k "cd backend && mvn spring-boot:run"

:: Wait a moment for backend to start
echo Waiting for backend to initialize...
timeout /t 5 /nobreak >nul

:: Start frontend in a new window
echo Starting frontend server...
start "Invoice Manager Frontend" cmd /k "cd frontend && npm run dev"

echo.
echo ========================================
echo Invoice Manager Started Successfully!
echo ========================================
echo.
echo Backend: Running in separate window
echo Frontend: Running in separate window
echo.
echo Access your application at:
echo   Local: http://localhost:3060
echo   Network: http://************:3060
echo.
echo From other devices on your network:
echo   http://************:3060
echo.
echo Press any key to close this window...
pause >nul
