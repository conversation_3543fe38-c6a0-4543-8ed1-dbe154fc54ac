@echo off
echo ========================================
echo Testing Projects API Fix
echo ========================================
echo.

:: Test the projects endpoint
echo 1. Testing Projects API Endpoint:
echo    URL: http://************:8091/projects
echo    Expected: Status 200 with JSON data
echo.

powershell -Command "try { $response = Invoke-RestMethod -Uri 'http://************:8091/projects' -Method Get; Write-Host 'SUCCESS: API returned data'; Write-Host 'Projects found:' $response.Length; Write-Host 'Sample project:'; $response[0] | Format-List } catch { Write-Host 'ERROR:' $_.Exception.Message }"

echo.
echo ========================================
echo 2. Testing Frontend Service:
echo ========================================
echo.

:: Check if frontend is running
echo Checking if frontend is running on port 3060...
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://localhost:3060' -Method Get -TimeoutSec 5; Write-Host 'Frontend is running on port 3060' } catch { Write-Host 'Frontend is NOT running on port 3060' }"

echo.
echo ========================================
echo 3. Next Steps:
echo ========================================
echo.

echo If API test shows SUCCESS:
echo   ✅ Backend is working correctly
echo   ✅ Projects data is available
echo.

echo If API test shows ERROR:
echo   ❌ Check if backend is running: mvn spring-boot:run
echo   ❌ Check database connection
echo.

echo To test the fix:
echo   1. Start frontend: cd frontend && npm run dev
echo   2. Open: http://localhost:3060/test-projects
echo   3. Check if projects display correctly
echo   4. Then test: http://localhost:3060/projects
echo.

echo ========================================
echo 4. Frontend Test URLs:
echo ========================================
echo.

echo Test Projects Page (debug):
echo   http://localhost:3060/test-projects
echo.

echo Main Projects Page:
echo   http://localhost:3060/projects
echo.

echo From other PC (replace localhost with IP):
echo   http://************:3060/test-projects
echo   http://************:3060/projects
echo.

echo ========================================
echo 5. Expected Results:
echo ========================================
echo.

echo Test Projects Page should show:
echo   ✅ API Connection: Success
echo   ✅ Data Retrieved: X projects
echo   ✅ Data Quality: Good (has names)
echo   ✅ Raw API Response with project details
echo.

echo Main Projects Page should show:
echo   ✅ Projects table with data
echo   ✅ No "Error loading projects" message
echo   ✅ No 500 errors in browser console
echo.

echo ========================================
echo 6. Troubleshooting:
echo ========================================
echo.

echo If Test Projects page shows data but Main Projects doesn't:
echo   - The API is working
echo   - The issue is in the Projects page component
echo   - Check browser console for errors
echo.

echo If Test Projects page shows no data:
echo   - Check backend logs for errors
echo   - Verify database has project records
echo   - Check network connectivity
echo.

echo ========================================
echo.
pause
