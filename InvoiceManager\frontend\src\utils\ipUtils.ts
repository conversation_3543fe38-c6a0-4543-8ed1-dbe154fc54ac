/**
 * Utility functions for handling IP addresses and API URLs
 */

/**
 * Get the base API URL based on the current environment
 * This will use the environment variable VITE_API_URL if available, otherwise fallback to dynamic hostname
 * @returns The base API URL
 */
export const getApiBaseUrl = (): string => {
  // First, check if we have an environment variable for the API URL
  const envApiUrl = import.meta.env.VITE_API_URL;

  if (envApiUrl && envApiUrl.trim() !== '') {
    // Use the environment variable as-is (it should include the full URL)
    // Remove /api suffix if present to get the base URL
    return envApiUrl.replace('/api', '');
  }

  // Fallback to dynamic hostname detection
  const hostname = window.location.hostname;
  const protocol = window.location.protocol;

  // For development mode, prefer localhost if accessing via localhost
  if (import.meta.env.DEV && (hostname === 'localhost' || hostname === '127.0.0.1')) {
    return 'http://localhost:8091';
  }

  // For all other cases (production, IP access, domain access), use the current hostname
  // This ensures the frontend can communicate with the backend on the same machine/network
  return `${protocol}//${hostname}:8091`;
};

/**
 * Get the API URL with the specified endpoint
 * @param endpoint The API endpoint
 * @returns The full API URL
 */
export const getApiUrl = (endpoint: string): string => {
  const baseUrl = getApiBaseUrl();

  // Ensure endpoint starts with a slash
  const formattedEndpoint = endpoint.startsWith('/') ? endpoint : `/${endpoint}`;

  return `${baseUrl}${formattedEndpoint}`;
};

/**
 * Check if the current hostname is localhost or our specific IP
 * @returns True if the current hostname is localhost, 127.0.0.1, or ************
 */
export const isLocalhost = (): boolean => {
  const hostname = window.location.hostname;
  return hostname === 'localhost' || hostname === '127.0.0.1' || hostname === '************';
};
