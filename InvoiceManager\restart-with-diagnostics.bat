@echo off
echo ========================================
echo Invoice Manager - Network Restart
echo ========================================
echo.

:: Check if we're in the right directory
if not exist "frontend" (
    echo Error: frontend directory not found!
    echo Please run this script from the InvoiceManager directory.
    pause
    exit /b 1
)

if not exist "backend" (
    echo Error: backend directory not found!
    echo Please run this script from the InvoiceManager directory.
    pause
    exit /b 1
)

:: Display current configuration
echo Current Network Configuration:
echo ==============================
type frontend\.env
echo.
echo ==============================
echo.

:: Kill any existing processes
echo Stopping existing processes...
taskkill /f /im node.exe 2>nul
taskkill /f /im java.exe 2>nul
echo.

:: Wait a moment
timeout /t 3 /nobreak >nul

:: Start backend in a new window
echo Starting backend server...
start "Invoice Manager Backend" cmd /k "cd backend && echo Starting backend on 0.0.0.0:8091... && mvn spring-boot:run"

:: Wait for backend to start
echo Waiting for backend to initialize...
timeout /t 10 /nobreak >nul

:: Start frontend in a new window
echo Starting frontend server...
start "Invoice Manager Frontend" cmd /k "cd frontend && echo Starting frontend on 0.0.0.0:3060... && npm run dev"

:: Wait for frontend to start
echo Waiting for frontend to initialize...
timeout /t 8 /nobreak >nul

echo.
echo ========================================
echo Services Started Successfully!
echo ========================================
echo.
echo Backend: Running on 0.0.0.0:8091
echo Frontend: Running on 0.0.0.0:3060
echo.
echo Access URLs:
echo   Local: http://localhost:3060
echo   Network: http://************:3060
echo.
echo From other devices: http://************:3060
echo.
echo ========================================
echo Diagnostic Commands:
echo ========================================
echo.
echo To test API endpoints from browser console:
echo   1. Open http://************:3060 on other PC
echo   2. Press F12 to open developer tools
echo   3. Go to Console tab
echo   4. Run: networkDiagnostics.runAll()
echo.
echo To test specific endpoints:
echo   curl http://************:8091/api/spocs
echo   curl http://************:8091/api/projects
echo   curl http://************:8091/api/invoices
echo   curl http://************:8091/api/payments/getAll
echo.
echo ========================================
echo Troubleshooting:
echo ========================================
echo.
echo If data still doesn't load:
echo 1. Check browser console for errors
echo 2. Verify firewall allows ports 3060 and 8091
echo 3. Test API endpoints directly
echo 4. Clear browser cache and refresh
echo.
echo Press any key to close this window...
pause >nul
