# Invoice Manager - Network Startup Script
# PowerShell version with better error handling and features

param(
    [string]$CustomIP = "",
    [switch]$UseLocalhost = $false,
    [switch]$Help = $false
)

# Color functions
function Write-Success { Write-Host $args -ForegroundColor Green }
function Write-Warning { Write-Host $args -ForegroundColor Yellow }
function Write-Error { Write-Host $args -ForegroundColor Red }
function Write-Info { Write-Host $args -ForegroundColor Cyan }

if ($Help) {
    Write-Info "=== Invoice Manager Network Startup ==="
    Write-Host ""
    Write-Host "Usage:"
    Write-Host "  .\start-network.ps1                    # Auto-detect IP and start"
    Write-Host "  .\start-network.ps1 -UseLocalhost      # Start with localhost"
    Write-Host "  .\start-network.ps1 -CustomIP *************  # Use specific IP"
    Write-Host "  .\start-network.ps1 -Help              # Show this help"
    Write-Host ""
    Write-Host "This script will:"
    Write-Host "  1. Configure network settings"
    Write-Host "  2. Start backend server"
    Write-Host "  3. Start frontend server"
    Write-Host "  4. Open application in browser"
    exit 0
}

Write-Info "=== Invoice Manager - Network Startup ==="
Write-Host ""

# Check if we're in the right directory
if (-not (Test-Path "backend") -or -not (Test-Path "frontend")) {
    Write-Error "Error: backend or frontend directory not found!"
    Write-Error "Please run this script from the InvoiceManager directory."
    Read-Host "Press Enter to exit"
    exit 1
}

# Check prerequisites
Write-Info "Checking prerequisites..."

# Check Java
try {
    $javaVersion = java -version 2>&1 | Select-String "version"
    Write-Success "✓ Java found: $($javaVersion -split '"')[1]"
} catch {
    Write-Error "✗ Java not found! Please install Java 17 or later."
    Read-Host "Press Enter to exit"
    exit 1
}

# Check Node.js
try {
    $nodeVersion = node --version
    Write-Success "✓ Node.js found: $nodeVersion"
} catch {
    Write-Error "✗ Node.js not found! Please install Node.js."
    Read-Host "Press Enter to exit"
    exit 1
}

# Check Maven
try {
    $mvnVersion = mvn --version 2>&1 | Select-String "Apache Maven" | Select-Object -First 1
    Write-Success "✓ Maven found: $($mvnVersion -split ' ')[2]"
} catch {
    Write-Error "✗ Maven not found! Please install Apache Maven."
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""

# Configure network settings
Write-Info "Configuring network settings..."
$configParams = @()
if ($UseLocalhost) { $configParams += "-UseLocalhost" }
if ($CustomIP) { $configParams += "-CustomIP", $CustomIP }

& ".\scripts\configure-network.ps1" @configParams

if ($LASTEXITCODE -ne 0) {
    Write-Error "Network configuration failed!"
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""
Write-Info "Starting Invoice Manager..."

# Start backend
Write-Info "Starting backend server..."
$backendJob = Start-Job -ScriptBlock {
    Set-Location $using:PWD
    Set-Location backend
    mvn spring-boot:run
}

Write-Success "✓ Backend started (Job ID: $($backendJob.Id))"

# Wait for backend to start
Write-Info "Waiting for backend to initialize..."
Start-Sleep -Seconds 8

# Start frontend
Write-Info "Starting frontend server..."
$frontendJob = Start-Job -ScriptBlock {
    Set-Location $using:PWD
    Set-Location frontend
    npm run dev
}

Write-Success "✓ Frontend started (Job ID: $($frontendJob.Id))"

# Wait for frontend to start
Write-Info "Waiting for frontend to initialize..."
Start-Sleep -Seconds 5

# Determine access URLs
$localUrl = "http://localhost:3060"
if ($UseLocalhost) {
    $networkUrl = $localUrl
} else {
    $ip = if ($CustomIP) { $CustomIP } else {
        (Get-NetIPConfiguration | Where-Object { $_.IPv4Address.IPAddress -notlike "169.254.*" -and $_.IPv4Address.IPAddress -ne "127.0.0.1" }).IPv4Address.IPAddress | Select-Object -First 1
    }
    $networkUrl = "http://$ip:3060"
}

Write-Host ""
Write-Success "=== Invoice Manager Started Successfully! ==="
Write-Host ""
Write-Info "🌐 Access URLs:"
Write-Success "   Local: $localUrl"
if ($networkUrl -ne $localUrl) {
    Write-Success "   Network: $networkUrl"
    Write-Host ""
    Write-Info "📱 From other devices on your network:"
    Write-Success "   $networkUrl"
}

Write-Host ""
Write-Info "🔧 Management:"
Write-Host "   Backend Job ID: $($backendJob.Id)"
Write-Host "   Frontend Job ID: $($frontendJob.Id)"
Write-Host ""
Write-Host "   To stop services:"
Write-Host "   Stop-Job $($backendJob.Id), $($frontendJob.Id)"
Write-Host "   Remove-Job $($backendJob.Id), $($frontendJob.Id)"

# Open browser
Write-Info "Opening application in browser..."
Start-Process $localUrl

Write-Host ""
Write-Warning "Press Ctrl+C to stop monitoring. Services will continue running in background."
Write-Host ""

# Monitor jobs
try {
    while ($true) {
        $backendState = Get-Job $backendJob.Id | Select-Object -ExpandProperty State
        $frontendState = Get-Job $frontendJob.Id | Select-Object -ExpandProperty State
        
        Write-Host "`rBackend: $backendState | Frontend: $frontendState" -NoNewline
        
        if ($backendState -eq "Failed" -or $frontendState -eq "Failed") {
            Write-Host ""
            Write-Error "One or more services failed!"
            break
        }
        
        Start-Sleep -Seconds 2
    }
} catch {
    Write-Host ""
    Write-Info "Monitoring stopped. Services are still running in background."
}

Write-Host ""
Write-Info "To stop all services later, run:"
Write-Host "Stop-Job $($backendJob.Id), $($frontendJob.Id); Remove-Job $($backendJob.Id), $($frontendJob.Id)"
