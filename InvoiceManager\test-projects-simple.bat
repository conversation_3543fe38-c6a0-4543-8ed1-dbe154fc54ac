@echo off
echo Testing Project CRUD Operations...

echo.
echo ===== Testing Backend Connection =====
curl -s http://192.168.1.30:8091/projects
if %errorlevel% equ 0 (
    echo Backend is running!
) else (
    echo Backend is not running. Please start it first.
    echo Run: cd backend && mvn spring-boot:run
    pause
    exit /b 1
)

echo.
echo ===== Testing Project Creation =====
curl -X POST http://192.168.1.30:8091/projects ^
  -H "Content-Type: application/json" ^
  -d "{\"name\":\"Test Project\",\"clientId\":1,\"value\":10000}"

echo.
echo ===== Testing Project Update =====
curl -X PUT http://192.168.1.30:8091/projects/1 ^
  -H "Content-Type: application/json" ^
  -d "{\"name\":\"Updated Test Project\",\"clientId\":1,\"value\":15000}"

echo.
echo ===== Testing Project Deletion =====
curl -X DELETE http://192.168.1.30:8091/projects/1

echo.
echo ===== CRUD Test Complete =====
echo All operations tested. Check the responses above.
echo.
echo Frontend is available at: http://192.168.1.30:3060/projects
echo.
pause
