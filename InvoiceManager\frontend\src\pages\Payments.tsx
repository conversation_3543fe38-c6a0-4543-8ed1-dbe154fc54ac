
import { useState, useEffect } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Search,
  Plus,
  Filter,
  ChevronDown,
  MoreHorizontal,
  Eye,
  FileText,
  Trash,
  CheckCircle,
  Clock,
  Calendar,
} from "lucide-react";
import { format } from "date-fns";
import ActionMenu from "@/components/shared/ActionMenu";
import StatusDropdown, { StatusOption } from "@/components/shared/StatusDropdown";
import RecordPaymentDialog from "@/components/payments/RecordPaymentDialog";
import PaymentDetails, { PaymentData } from "@/components/payments/PaymentDetails";
import { toast } from "sonner";
import { paymentService, type Payment } from "@/services/paymentService";

// Mock data - would be fetched from API in real application
const paymentsData = [
  {
    id: "PMT-2023-001",
    invoiceId: "INV-2023-001",
    client: "Acme Corporation",
    amount: "$14,750",
    date: "2023-02-10",
    method: "Bank Transfer",
    status: "Completed",
    notes: "Payment received for website redesign project",
  },
  {
    id: "PMT-2023-002",
    invoiceId: "INV-2023-005",
    client: "Acme Corporation",
    amount: "$5,900",
    date: "2023-04-12",
    method: "Credit Card",
    status: "Completed",
    notes: "Monthly maintenance payment",
  },
  {
    id: "PMT-2023-003",
    invoiceId: "INV-2023-003",
    client: "Wayne Enterprises",
    amount: "$21,830",
    date: "2023-03-20",
    method: "Check",
    status: "Pending",
    notes: "Check in mail - waiting for clearance",
  },
];

const bdmPaymentsData = [
  {
    id: "BDM-2023-001",
    bdmName: "Sarah Johnson",
    client: "Acme Corporation",
    amount: "$737.50",
    date: "2023-02-15",
    commissionRate: "5%",
    invoiceId: "INV-2023-001",
    isPaid: true,
  },
  {
    id: "BDM-2023-002",
    bdmName: "Sarah Johnson",
    client: "Acme Corporation",
    amount: "$295",
    date: "2023-04-15",
    commissionRate: "5%",
    invoiceId: "INV-2023-005",
    isPaid: true,
  },
  {
    id: "BDM-2023-003",
    bdmName: "Michael Brown",
    client: "Globex Inc.",
    amount: "$1,275",
    date: "2023-03-05",
    commissionRate: "4.5%",
    invoiceId: "INV-2023-002",
    isPaid: false,
  },
  {
    id: "BDM-2023-004",
    bdmName: "Emily Davis",
    client: "Wayne Enterprises",
    amount: "$1,200",
    date: "2023-03-20",
    commissionRate: "5.5%",
    invoiceId: "INV-2023-003",
    isPaid: false,
  },
];

// Define payment status options
const paymentStatusOptions: StatusOption[] = [
  { value: "Completed", label: "Completed", color: "bg-green-100 text-green-800 border-green-200" },
  { value: "Pending", label: "Pending", color: "bg-yellow-100 text-yellow-800 border-yellow-200" },
  { value: "Failed", label: "Failed", color: "bg-red-100 text-red-800 border-red-200" },
];

// Define BDM payment status options
const bdmPaymentStatusOptions: StatusOption[] = [
  { value: "Paid", label: "Paid", color: "bg-green-100 text-green-800 border-green-200" },
  { value: "Unpaid", label: "Unpaid", color: "bg-yellow-100 text-yellow-800 border-yellow-200" },
];

const Payments = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [payments, setPayments] = useState<any[]>([]);
  const [bdmPayments, setBdmPayments] = useState(bdmPaymentsData);
  const [filteredPayments, setFilteredPayments] = useState<any[]>([]);
  const [filteredBdmPayments, setFilteredBdmPayments] = useState(bdmPaymentsData);
  const [statusFilter, setStatusFilter] = useState("all");
  const [bdmStatusFilter, setBdmStatusFilter] = useState("all");
  const [isRecordPaymentDialogOpen, setIsRecordPaymentDialogOpen] = useState(false);
  const [isPaymentDetailsOpen, setIsPaymentDetailsOpen] = useState(false);
  const [selectedPayment, setSelectedPayment] = useState<PaymentData | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Function to load payments from API
  const loadPayments = async () => {
    try {
      setIsLoading(true);
      console.log('Loading payments from API...');

      // First check if backend is running
      try {
        // Get server URL from environment
        const serverUrl = import.meta.env.VITE_API_BASE_URL || 'http://192.168.1.30:8091';

        const healthCheck = await fetch(`${serverUrl}/api/payments/getAll`, {
          method: 'GET',
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
          },
          credentials: 'omit'
        });
        console.log('Backend health check status:', healthCheck.status);
      } catch (healthError) {
        console.error('Backend appears to be down:', healthError);
        toast.error('Backend server is not running. Please start the backend server.');
        setIsLoading(false);
        return;
      }

      const apiPayments = await paymentService.getAllPayments();
      console.log('Loaded payments:', apiPayments);

      // Transform API payments to match the expected format
      const transformedPayments = apiPayments.map((payment: Payment) => ({
        id: `PMT-${payment.id}`,
        invoiceId: payment.invoiceNumber || `INV-${payment.invoiceId}` || 'Unknown',
        client: payment.clientName || 'Unknown Client',
        amount: `₹${payment.amountReceived?.toLocaleString() || '0'}`,
        date: payment.receivedOn || new Date().toISOString().split('T')[0],
        method: payment.paymentMode || 'Unknown',
        status: 'Completed',
        notes: payment.referenceNumber || '',
        databaseId: payment.id,
        originalData: payment
      }));

      setPayments(transformedPayments);
      setFilteredPayments(transformedPayments);
      console.log('Transformed payments:', transformedPayments);
    } catch (error) {
      console.error('Error loading payments:', error);
      toast.error('Failed to load payments');
      // Fallback to mock data if API fails
      setPayments(paymentsData);
      setFilteredPayments(paymentsData);
    } finally {
      setIsLoading(false);
    }
  };

  // Load payments when component mounts
  useEffect(() => {
    loadPayments();
  }, []);

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const term = e.target.value.toLowerCase();
    setSearchTerm(term);

    // Filter for regular payments
    let filtered = payments.filter(
      (payment) =>
        payment.id.toLowerCase().includes(term) ||
        payment.invoiceId.toLowerCase().includes(term) ||
        payment.client.toLowerCase().includes(term) ||
        payment.method.toLowerCase().includes(term)
    );

    if (statusFilter !== "all") {
      filtered = filtered.filter(payment => payment.status.toLowerCase() === statusFilter.toLowerCase());
    }

    setFilteredPayments(filtered);

    // Filter for BDM payments
    let filteredBdm = bdmPayments.filter(
      (payment) =>
        payment.id.toLowerCase().includes(term) ||
        payment.bdmName.toLowerCase().includes(term) ||
        payment.client.toLowerCase().includes(term) ||
        payment.invoiceId.toLowerCase().includes(term)
    );

    if (bdmStatusFilter !== "all") {
      const isPaid = bdmStatusFilter === "paid";
      filteredBdm = filteredBdm.filter(payment => payment.isPaid === isPaid);
    }

    setFilteredBdmPayments(filteredBdm);
  };

  const filterPaymentsByStatus = (status: string) => {
    setStatusFilter(status);

    if (status === "all") {
      const filtered = payments.filter(
        (payment) =>
          payment.id.toLowerCase().includes(searchTerm) ||
          payment.invoiceId.toLowerCase().includes(searchTerm) ||
          payment.client.toLowerCase().includes(searchTerm) ||
          payment.method.toLowerCase().includes(searchTerm)
      );
      setFilteredPayments(filtered);
    } else {
      const filtered = payments.filter(
        (payment) =>
          payment.status.toLowerCase() === status.toLowerCase() &&
          (payment.id.toLowerCase().includes(searchTerm) ||
           payment.invoiceId.toLowerCase().includes(searchTerm) ||
           payment.client.toLowerCase().includes(searchTerm) ||
           payment.method.toLowerCase().includes(searchTerm))
      );
      setFilteredPayments(filtered);
    }
  };

  const filterBdmPaymentsByStatus = (status: string) => {
    setBdmStatusFilter(status);

    if (status === "all") {
      const filtered = bdmPayments.filter(
        (payment) =>
          payment.id.toLowerCase().includes(searchTerm) ||
          payment.bdmName.toLowerCase().includes(searchTerm) ||
          payment.client.toLowerCase().includes(searchTerm) ||
          payment.invoiceId.toLowerCase().includes(searchTerm)
      );
      setFilteredBdmPayments(filtered);
    } else {
      const isPaid = status === "paid";
      const filtered = bdmPayments.filter(
        (payment) =>
          payment.isPaid === isPaid &&
          (payment.id.toLowerCase().includes(searchTerm) ||
           payment.bdmName.toLowerCase().includes(searchTerm) ||
           payment.client.toLowerCase().includes(searchTerm) ||
           payment.invoiceId.toLowerCase().includes(searchTerm))
      );
      setFilteredBdmPayments(filtered);
    }
  };

  const handlePaymentStatusChange = (id: string, newStatus: string) => {
    // Update the payments state
    const updatedPayments = payments.map(payment =>
      payment.id === id ? { ...payment, status: newStatus } : payment
    );

    setPayments(updatedPayments);

    // Update the filtered payments as well
    const updatedFilteredPayments = filteredPayments.map(payment =>
      payment.id === id ? { ...payment, status: newStatus } : payment
    );

    setFilteredPayments(updatedFilteredPayments);

    // Show success message
    toast.success(`Payment status updated to ${newStatus}`, {
      description: `Payment ID: ${id} status has been updated.`,
    });
  };

  const handleBdmPaymentStatusChange = (id: string, isPaid: boolean) => {
    // Update the bdmPayments state
    const updatedBdmPayments = bdmPayments.map(payment =>
      payment.id === id ? { ...payment, isPaid } : payment
    );

    setBdmPayments(updatedBdmPayments);

    // Update the filtered bdmPayments as well
    const updatedFilteredBdmPayments = filteredBdmPayments.map(payment =>
      payment.id === id ? { ...payment, isPaid } : payment
    );

    setFilteredBdmPayments(updatedFilteredBdmPayments);

    // Show success message
    toast.success(`BDM payment marked as ${isPaid ? 'Paid' : 'Unpaid'}`, {
      description: `Payment ID: ${id} status has been updated.`,
    });
  };

  const handleViewPayment = (id: string) => {
    console.log("View payment clicked:", id);

    try {
      // Find the payment by ID
      const payment = payments.find(p => p.id === id);

      if (payment) {
        console.log("Found regular payment:", payment);
        setSelectedPayment(payment);
        setIsPaymentDetailsOpen(true);
      } else {
        // Check if it's a BDM payment
        const bdmPayment = bdmPayments.find(p => p.id === id);

        if (bdmPayment) {
          console.log("Found BDM payment:", bdmPayment);
          // Convert BDM payment to regular payment format for the dialog
          const formattedPayment: PaymentData = {
            id: bdmPayment.id,
            invoiceId: bdmPayment.invoiceId,
            client: bdmPayment.client,
            amount: bdmPayment.amount,
            date: bdmPayment.date,
            method: "Commission",
            status: bdmPayment.isPaid ? "Completed" : "Pending",
            notes: `Commission payment for BDM: ${bdmPayment.bdmName}. Commission rate: ${bdmPayment.commissionRate}.`
          };

          setSelectedPayment(formattedPayment);
          setIsPaymentDetailsOpen(true);
        } else {
          console.error("Payment not found:", id);
          toast.error("Payment not found");
        }
      }
    } catch (error) {
      console.error("Error viewing payment:", error);
      toast.error("Error viewing payment details");
    }
  };

  const handleDeletePayment = (id: string) => {
    // Update the payments state by removing the deleted payment
    const updatedPayments = payments.filter(payment => payment.id !== id);
    setPayments(updatedPayments);

    // Update the filtered payments as well
    const updatedFilteredPayments = filteredPayments.filter(payment => payment.id !== id);
    setFilteredPayments(updatedFilteredPayments);

    // Show success message
    toast.success(`Payment deleted successfully`, {
      description: `Payment ID: ${id} has been removed.`,
    });
  };

  const handleDeleteBdmPayment = (id: string) => {
    // Update the bdmPayments state by removing the deleted payment
    const updatedBdmPayments = bdmPayments.filter(payment => payment.id !== id);
    setBdmPayments(updatedBdmPayments);

    // Update the filtered bdmPayments as well
    const updatedFilteredBdmPayments = filteredBdmPayments.filter(payment => payment.id !== id);
    setFilteredBdmPayments(updatedFilteredBdmPayments);

    // Show success message
    toast.success(`BDM payment deleted successfully`, {
      description: `Payment ID: ${id} has been removed.`,
    });
  };

  const handleRecordPayment = async (newPayment: any) => {
    // Reload payments from API to get the latest data including the new payment
    await loadPayments();

    toast.success("Payment recorded successfully", {
      description: `Payment of ${newPayment.amount} has been recorded and is now visible in the payments list.`,
    });
  };

  return (
    <div className="space-y-6 animate-fade-in">
      <div>
        <h2 className="text-3xl font-bold tracking-tight">Payments</h2>
        <p className="text-muted-foreground">Track payment status and manage transactions.</p>
      </div>

      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-2 sm:space-y-0">
        <div className="relative">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            type="search"
            placeholder="Search payments..."
            className="pl-8 w-full md:w-[300px]"
            value={searchTerm}
            onChange={handleSearch}
          />
        </div>
        <Button onClick={() => setIsRecordPaymentDialogOpen(true)}>
          <Plus className="mr-2 h-4 w-4" />
          Record Payment
        </Button>
      </div>

      <Tabs defaultValue="client-payments">
        <TabsList className="w-full sm:w-auto">
          <TabsTrigger value="client-payments" className="flex-1 sm:flex-initial">Client Payments</TabsTrigger>
          <TabsTrigger value="bdm-payments" className="flex-1 sm:flex-initial">BDM Commissions</TabsTrigger>
        </TabsList>

        <TabsContent value="client-payments" className="space-y-4">
          <div className="flex justify-end">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm">
                  <Filter className="mr-2 h-4 w-4" />
                  Filter
                  <ChevronDown className="ml-2 h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel>Filter by Status</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={() => filterPaymentsByStatus("all")}>
                  All
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => filterPaymentsByStatus("completed")}>
                  <CheckCircle className="mr-2 h-4 w-4 text-green-500" />
                  Completed
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => filterPaymentsByStatus("pending")}>
                  <Clock className="mr-2 h-4 w-4 text-yellow-500" />
                  Pending
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Client Payments</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border overflow-hidden">
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Payment ID</TableHead>
                        <TableHead>Invoice</TableHead>
                        <TableHead>Client</TableHead>
                        <TableHead>Amount</TableHead>
                        <TableHead>Date</TableHead>
                        <TableHead>Method</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {isLoading ? (
                        <TableRow>
                          <TableCell colSpan={8} className="text-center py-6">
                            Loading payments...
                          </TableCell>
                        </TableRow>
                      ) : filteredPayments.length === 0 ? (
                        <TableRow>
                          <TableCell colSpan={8} className="text-center py-6 text-muted-foreground">
                            No payments found
                          </TableCell>
                        </TableRow>
                      ) : (
                        filteredPayments.map((payment) => (
                          <TableRow key={payment.id}>
                            <TableCell className="font-medium">{payment.id}</TableCell>
                            <TableCell>{payment.invoiceId}</TableCell>
                            <TableCell>{payment.client}</TableCell>
                            <TableCell>{payment.amount}</TableCell>
                          <TableCell>{format(new Date(payment.date), "MMM d, yyyy")}</TableCell>
                          <TableCell>{payment.method}</TableCell>
                          <TableCell>
                            <StatusDropdown
                              currentStatus={payment.status}
                              onStatusChange={(newStatus) => handlePaymentStatusChange(payment.id, newStatus)}
                              statusOptions={paymentStatusOptions}
                            />
                          </TableCell>
                          <TableCell className="text-right">
                            <ActionMenu
                              itemId={payment.id}
                              onView={handleViewPayment}
                              onDelete={handleDeletePayment}
                              viewLabel="View Details"
                              deleteLabel="Delete Payment"
                              deleteDialogTitle="Delete Payment"
                              deleteDialogDescription={`Are you sure you want to delete payment ${payment.id}? This action cannot be undone.`}
                            />
                          </TableCell>
                        </TableRow>
                        ))
                      )}
                    </TableBody>
                  </Table>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="bdm-payments" className="space-y-4">
          <div className="flex justify-end">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm">
                  <Filter className="mr-2 h-4 w-4" />
                  Filter
                  <ChevronDown className="ml-2 h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel>Filter by Status</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={() => filterBdmPaymentsByStatus("all")}>
                  All
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => filterBdmPaymentsByStatus("paid")}>
                  <CheckCircle className="mr-2 h-4 w-4 text-green-500" />
                  Paid
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => filterBdmPaymentsByStatus("unpaid")}>
                  <Clock className="mr-2 h-4 w-4 text-yellow-500" />
                  Unpaid
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>BDM Commission Payments</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border overflow-hidden">
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Payment ID</TableHead>
                        <TableHead>BDM Name</TableHead>
                        <TableHead>Client</TableHead>
                        <TableHead>Amount</TableHead>
                        <TableHead>Commission Rate</TableHead>
                        <TableHead>Invoice</TableHead>
                        <TableHead>Date</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredBdmPayments.map((payment) => (
                        <TableRow key={payment.id}>
                          <TableCell className="font-medium">{payment.id}</TableCell>
                          <TableCell>{payment.bdmName}</TableCell>
                          <TableCell>{payment.client}</TableCell>
                          <TableCell>{payment.amount}</TableCell>
                          <TableCell>{payment.commissionRate}</TableCell>
                          <TableCell>{payment.invoiceId}</TableCell>
                          <TableCell>{format(new Date(payment.date), "MMM d, yyyy")}</TableCell>
                          <TableCell>
                            <StatusDropdown
                              currentStatus={payment.isPaid ? "Paid" : "Unpaid"}
                              onStatusChange={(newStatus) => handleBdmPaymentStatusChange(payment.id, newStatus === "Paid")}
                              statusOptions={bdmPaymentStatusOptions}
                            />
                          </TableCell>
                          <TableCell className="text-right">
                            <ActionMenu
                              itemId={payment.id}
                              onView={handleViewPayment}
                              onDelete={handleDeleteBdmPayment}
                              viewLabel="View Details"
                              deleteLabel="Delete Payment"
                              deleteDialogTitle="Delete BDM Payment"
                              deleteDialogDescription={`Are you sure you want to delete BDM payment ${payment.id}? This action cannot be undone.`}
                            />
                          </TableCell>
                        </TableRow>
                      ))}
                      {filteredBdmPayments.length === 0 && (
                        <TableRow>
                          <TableCell colSpan={9} className="text-center py-6 text-muted-foreground">
                            No BDM payments found
                          </TableCell>
                        </TableRow>
                      )}
                    </TableBody>
                  </Table>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Record Payment Dialog */}
      <RecordPaymentDialog
        open={isRecordPaymentDialogOpen}
        onOpenChange={setIsRecordPaymentDialogOpen}
        onPaymentRecorded={handleRecordPayment}
      />

      {/* Payment Details */}
      <PaymentDetails
        payment={selectedPayment}
        open={isPaymentDetailsOpen}
        onClose={() => setIsPaymentDetailsOpen(false)}
      />
    </div>
  );
};

export default Payments;
