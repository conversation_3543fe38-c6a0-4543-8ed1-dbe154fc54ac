@echo off
echo ========================================
echo FIXING 500 ERROR - USE WORKING ENDPOINT
echo ========================================
echo.

echo Problem: 500 Internal Server Error on most endpoints, but /projects/getAll works with auth
echo Solution: Update all services to use the working endpoint with authentication
echo.

echo ========================================
echo Step 1: Stop All Services
echo ========================================
echo.

echo Stopping backend and frontend...
taskkill /f /im java.exe 2>nul
taskkill /f /im node.exe 2>nul
timeout /t 3 /nobreak >nul

echo ========================================
echo Step 2: Start Backend
echo ========================================
echo.

echo Starting backend...
cd backend

echo Compiling backend...
mvn clean compile -q
if %ERRORLEVEL% neq 0 (
    echo ERROR: Backend compilation failed!
    pause
    exit /b 1
)

echo Starting backend...
start "Backend - Working Endpoint" cmd /k "echo Backend starting... && mvn spring-boot:run"

echo Waiting for backend to start...
timeout /t 20 /nobreak >nul

cd ..

echo ========================================
echo Step 3: Test Working Endpoint
echo ========================================
echo.

echo Testing the working endpoint with authentication...
powershell -Command "$auth = [Convert]::ToBase64String([Text.Encoding]::ASCII.GetBytes('admin:admin123')); try { $response = Invoke-RestMethod -Uri 'http://************:8091/projects/getAll' -Method Get -Headers @{'Authorization'='Basic '+$auth}; Write-Host 'SUCCESS: Working endpoint /projects/getAll'; Write-Host 'Projects found:' $response.Length; if ($response.Length -gt 0) { Write-Host 'First project:' $response[0].name 'Client:' $response[0].client.name } } catch { Write-Host 'ERROR: Working endpoint failed -' $_.Exception.Message }"

echo.
echo Testing other endpoints for comparison...

echo Testing /projects (without auth)...
powershell -Command "try { $response = Invoke-RestMethod -Uri 'http://************:8091/projects' -Method Get; Write-Host '/projects: SUCCESS' } catch { Write-Host '/projects: FAILED -' $_.Exception.Message }"

echo Testing /api/projects (without auth)...
powershell -Command "try { $response = Invoke-RestMethod -Uri 'http://************:8091/api/projects' -Method Get; Write-Host '/api/projects: SUCCESS' } catch { Write-Host '/api/projects: FAILED -' $_.Exception.Message }"

echo ========================================
echo Step 4: Start Frontend
echo ========================================
echo.

echo Starting frontend with working endpoint configuration...
cd frontend

echo Starting frontend...
start "Frontend - Working Endpoint" cmd /k "echo Frontend starting with working endpoint... && npm run dev -- --host 0.0.0.0 --port 3060"

echo Waiting for frontend to start...
timeout /t 15 /nobreak >nul

cd ..

echo ========================================
echo Step 5: Test Frontend
echo ========================================
echo.

echo Testing frontend accessibility...
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://************:3060' -Method Get -TimeoutSec 10; Write-Host 'SUCCESS: Frontend accessible' } catch { Write-Host 'ERROR: Frontend not accessible:' $_.Exception.Message }"

echo ========================================
echo Step 6: Create Working Endpoint Test
echo ========================================
echo.

echo Creating test page for working endpoint...

:: Create test page
echo ^<!DOCTYPE html^> > test-working-endpoint.html
echo ^<html^> >> test-working-endpoint.html
echo ^<head^> >> test-working-endpoint.html
echo     ^<title^>Working Endpoint Test^</title^> >> test-working-endpoint.html
echo     ^<style^> >> test-working-endpoint.html
echo         body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; } >> test-working-endpoint.html
echo         .container { max-width: 800px; margin: 0 auto; } >> test-working-endpoint.html
echo         .test-section { margin: 20px 0; padding: 20px; background: white; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); } >> test-working-endpoint.html
echo         .success { border-left: 4px solid #4CAF50; } >> test-working-endpoint.html
echo         .error { border-left: 4px solid #f44336; } >> test-working-endpoint.html
echo         button { padding: 12px 20px; margin: 8px; cursor: pointer; border: none; border-radius: 4px; background: #2196F3; color: white; } >> test-working-endpoint.html
echo         button:hover { background: #1976D2; } >> test-working-endpoint.html
echo         pre { background: #f8f9fa; padding: 15px; border-radius: 4px; overflow-x: auto; } >> test-working-endpoint.html
echo     ^</style^> >> test-working-endpoint.html
echo ^</head^> >> test-working-endpoint.html
echo ^<body^> >> test-working-endpoint.html
echo     ^<div class="container"^> >> test-working-endpoint.html
echo         ^<h1^>🔧 Working Endpoint Test^</h1^> >> test-working-endpoint.html
echo         ^<p^>This tests the working /projects/getAll endpoint with authentication.^</p^> >> test-working-endpoint.html
echo         ^<div class="test-section"^> >> test-working-endpoint.html
echo             ^<h3^>Test 1: Working Endpoint (with auth)^</h3^> >> test-working-endpoint.html
echo             ^<button onclick="testWorkingEndpoint()"^>Test /projects/getAll^</button^> >> test-working-endpoint.html
echo             ^<div id="result1"^>^</div^> >> test-working-endpoint.html
echo         ^</div^> >> test-working-endpoint.html
echo         ^<div class="test-section"^> >> test-working-endpoint.html
echo             ^<h3^>Test 2: Broken Endpoint (no auth)^</h3^> >> test-working-endpoint.html
echo             ^<button onclick="testBrokenEndpoint()"^>Test /projects^</button^> >> test-working-endpoint.html
echo             ^<div id="result2"^>^</div^> >> test-working-endpoint.html
echo         ^</div^> >> test-working-endpoint.html
echo         ^<div class="test-section"^> >> test-working-endpoint.html
echo             ^<h3^>Test 3: Frontend Application^</h3^> >> test-working-endpoint.html
echo             ^<button onclick="window.open('http://************:3060/simple-projects', '_blank')"^>Open Simple Projects^</button^> >> test-working-endpoint.html
echo             ^<p^>Should now work with the working endpoint!^</p^> >> test-working-endpoint.html
echo         ^</div^> >> test-working-endpoint.html
echo     ^</div^> >> test-working-endpoint.html
echo     ^<script^> >> test-working-endpoint.html
echo         function testWorkingEndpoint() { >> test-working-endpoint.html
echo             const result = document.getElementById('result1'); >> test-working-endpoint.html
echo             result.innerHTML = '^<p^>Testing working endpoint...^</p^>'; >> test-working-endpoint.html
echo             result.className = 'test-section'; >> test-working-endpoint.html
echo             const auth = btoa('admin:admin123'); >> test-working-endpoint.html
echo             fetch('http://************:8091/projects/getAll', { >> test-working-endpoint.html
echo                 method: 'GET', >> test-working-endpoint.html
echo                 headers: { >> test-working-endpoint.html
echo                     'Accept': 'application/json', >> test-working-endpoint.html
echo                     'Content-Type': 'application/json', >> test-working-endpoint.html
echo                     'Authorization': 'Basic ' + auth >> test-working-endpoint.html
echo                 }, >> test-working-endpoint.html
echo                 credentials: 'omit' >> test-working-endpoint.html
echo             }) >> test-working-endpoint.html
echo                 .then(response =^> { >> test-working-endpoint.html
echo                     if (response.ok) { >> test-working-endpoint.html
echo                         return response.json(); >> test-working-endpoint.html
echo                     } else { >> test-working-endpoint.html
echo                         throw new Error('HTTP ' + response.status + ': ' + response.statusText); >> test-working-endpoint.html
echo                     } >> test-working-endpoint.html
echo                 }) >> test-working-endpoint.html
echo                 .then(data =^> { >> test-working-endpoint.html
echo                     result.className = 'test-section success'; >> test-working-endpoint.html
echo                     result.innerHTML = '^<h4^>✅ SUCCESS!^</h4^>' + >> test-working-endpoint.html
echo                         '^<p^>Found ' + (Array.isArray(data) ? data.length : 1) + ' project(s)^</p^>' + >> test-working-endpoint.html
echo                         '^<pre^>' + JSON.stringify(data, null, 2) + '^</pre^>'; >> test-working-endpoint.html
echo                 }) >> test-working-endpoint.html
echo                 .catch(error =^> { >> test-working-endpoint.html
echo                     result.className = 'test-section error'; >> test-working-endpoint.html
echo                     result.innerHTML = '^<h4^>❌ ERROR^</h4^>^<p^>' + error.message + '^</p^>'; >> test-working-endpoint.html
echo                 }); >> test-working-endpoint.html
echo         } >> test-working-endpoint.html
echo         function testBrokenEndpoint() { >> test-working-endpoint.html
echo             const result = document.getElementById('result2'); >> test-working-endpoint.html
echo             result.innerHTML = '^<p^>Testing broken endpoint...^</p^>'; >> test-working-endpoint.html
echo             result.className = 'test-section'; >> test-working-endpoint.html
echo             fetch('http://************:8091/projects') >> test-working-endpoint.html
echo                 .then(response =^> { >> test-working-endpoint.html
echo                     if (response.ok) { >> test-working-endpoint.html
echo                         return response.json(); >> test-working-endpoint.html
echo                     } else { >> test-working-endpoint.html
echo                         throw new Error('HTTP ' + response.status + ': ' + response.statusText); >> test-working-endpoint.html
echo                     } >> test-working-endpoint.html
echo                 }) >> test-working-endpoint.html
echo                 .then(data =^> { >> test-working-endpoint.html
echo                     result.className = 'test-section success'; >> test-working-endpoint.html
echo                     result.innerHTML = '^<h4^>✅ UNEXPECTED SUCCESS!^</h4^>' + >> test-working-endpoint.html
echo                         '^<p^>This endpoint worked unexpectedly^</p^>' + >> test-working-endpoint.html
echo                         '^<pre^>' + JSON.stringify(data, null, 2) + '^</pre^>'; >> test-working-endpoint.html
echo                 }) >> test-working-endpoint.html
echo                 .catch(error =^> { >> test-working-endpoint.html
echo                     result.className = 'test-section error'; >> test-working-endpoint.html
echo                     result.innerHTML = '^<h4^>❌ EXPECTED ERROR^</h4^>^<p^>' + error.message + '^</p^>^<p^>This is expected - this endpoint returns 500 error^</p^>'; >> test-working-endpoint.html
echo                 }); >> test-working-endpoint.html
echo         } >> test-working-endpoint.html
echo     ^</script^> >> test-working-endpoint.html
echo ^</body^> >> test-working-endpoint.html
echo ^</html^> >> test-working-endpoint.html

echo ✅ Created test-working-endpoint.html

echo ========================================
echo TESTING RESULTS
echo ========================================
echo.

echo The fix has been applied! All services now use the working endpoint.
echo.

echo Test from ANOTHER PC:
echo.

echo 1. Working Endpoint Test:
echo    Copy test-working-endpoint.html to another PC
echo    Open in browser and test the working endpoint
echo    Expected: SUCCESS with your real project data
echo.

echo 2. Frontend Application:
echo    Open browser: http://************:3060/simple-projects
echo    Expected: Real project data loads immediately (no 500 errors)
echo.

echo 3. Direct API Test:
echo    Open browser: http://************:8091/projects/getAll
echo    Expected: May prompt for login (admin/admin123) then show JSON data
echo.

echo ========================================
echo EXPECTED RESULTS
echo ========================================
echo.

echo ✅ When working correctly:
echo   - Frontend loads project data immediately
echo   - No 500 Internal Server Error messages
echo   - Shows your real project: "Website Development" with client "saurabh"
echo   - No retry needed - works on first load
echo.

echo ❌ If still not working:
echo   - Check backend console for database connection errors
echo   - Verify admin:admin123 credentials are correct
echo   - Check if database has project data: SELECT * FROM projects;
echo.

echo ========================================
echo.

echo Fix complete! The working endpoint is now used everywhere.
echo Test from another PC - should work immediately without retry!
echo.

pause
