import { api } from './api';

export interface StaffingType {
  id?: number | string;
  name: string;
  description?: string;
}

class StaffingTypeService {
  private get baseUrl() {
    return import.meta.env.VITE_API_BASE_URL || 'http://************:8091';
  }

  // Helper method to get auth headers
  private getAuthHeaders(): Record<string, string> {
    // For basic auth (used in development)
    return {
      'Authorization': 'Basic ' + btoa('admin:admin123'),
      'Content-Type': 'application/json'
    };
  }

  /**
   * Get all staffing types
   * @returns Promise with array of staffing types
   */
  async getAllStaffingTypes(): Promise<StaffingType[]> {
    console.log('StaffingTypeService: Fetching all staffing types');

    try {
      // Try using the api utility first
      return await api.getStaffingTypes();
    } catch (error) {
      console.error('StaffingTypeService: Error fetching staffing types via api utility:', error);

      // If API method fails, try direct fetch
      console.log('StaffingTypeService: Trying alternative approach to fetch staffing types');

      // Try multiple endpoints
      const endpoints = [
        `${this.baseUrl}/api/noauth/staffing-types`,
        `${this.baseUrl}/staffing-types/getAll`,
        `${this.baseUrl}/api/staffing-types`,
        `${this.baseUrl}/api/v1/staffing-types`,
        `${this.baseUrl}/staffing-types`,
        `${this.baseUrl}/api/noauth/staffing-types`
      ];

      for (const endpoint of endpoints) {
        try {
          console.log(`StaffingTypeService: Trying endpoint: ${endpoint}`);
          const response = await fetch(endpoint, {
            method: 'GET',
            headers: this.getAuthHeaders()
          });

          if (!response.ok) {
            console.warn(`StaffingTypeService: Endpoint ${endpoint} returned status ${response.status}`);
            continue;
          }

          const data = await response.json();
          console.log(`StaffingTypeService: Successfully fetched staffing types from ${endpoint}:`, data);
          return data;
        } catch (endpointError) {
          console.warn(`StaffingTypeService: Error fetching from ${endpoint}:`, endpointError);
        }
      }

      // If all endpoints fail, throw the original error
      throw error;
    }
  }

  /**
   * Get staffing type by ID
   * @param id Staffing type ID
   * @returns Promise with staffing type
   */
  async getStaffingTypeById(id: number | string): Promise<StaffingType> {
    console.log(`StaffingTypeService: Fetching staffing type with ID ${id}`);

    try {
      // Try using the api utility first
      return await api.getStaffingType(Number(id));
    } catch (error) {
      console.error(`StaffingTypeService: Error fetching staffing type with ID ${id} via api utility:`, error);

      // If API method fails, try direct fetch
      console.log('StaffingTypeService: Trying alternative approach to fetch staffing type');

      // Try multiple endpoints
      const endpoints = [
        `${this.baseUrl}/staffing-types/getById/${id}`,
        `${this.baseUrl}/api/staffing-types/${id}`,
        `${this.baseUrl}/api/v1/staffing-types/${id}`
      ];

      for (const endpoint of endpoints) {
        try {
          console.log(`StaffingTypeService: Trying endpoint: ${endpoint}`);
          const response = await fetch(endpoint, {
            method: 'GET',
            headers: this.getAuthHeaders()
          });

          if (!response.ok) {
            console.warn(`StaffingTypeService: Endpoint ${endpoint} returned status ${response.status}`);
            continue;
          }

          const data = await response.json();
          console.log(`StaffingTypeService: Successfully fetched staffing type from ${endpoint}:`, data);
          return data;
        } catch (endpointError) {
          console.warn(`StaffingTypeService: Error fetching from ${endpoint}:`, endpointError);
        }
      }

      // If all endpoints fail, throw the original error
      throw error;
    }
  }

  /**
   * Create a new staffing type
   * @param staffingType Staffing type data
   * @returns Promise with created staffing type
   */
  async createStaffingType(staffingType: StaffingType): Promise<StaffingType> {
    console.log('StaffingTypeService: Creating staffing type:', staffingType);

    // Try multiple endpoints
    const endpoints = [
      `${this.baseUrl}/staffing-types/create`,
      `${this.baseUrl}/api/staffing-types`,
      `${this.baseUrl}/api/v1/staffing-types`
    ];

    for (const endpoint of endpoints) {
      try {
        console.log(`StaffingTypeService: Trying endpoint: ${endpoint}`);
        const response = await fetch(endpoint, {
          method: 'POST',
          headers: this.getAuthHeaders(),
          body: JSON.stringify(staffingType)
        });

        if (!response.ok) {
          console.warn(`StaffingTypeService: Endpoint ${endpoint} returned status ${response.status}`);
          continue;
        }

        const data = await response.json();
        console.log(`StaffingTypeService: Successfully created staffing type from ${endpoint}:`, data);
        return data;
      } catch (endpointError) {
        console.warn(`StaffingTypeService: Error creating from ${endpoint}:`, endpointError);
      }
    }

    throw new Error('Failed to create staffing type');
  }

  /**
   * Update an existing staffing type
   * @param id Staffing type ID
   * @param staffingType Staffing type data
   * @returns Promise with updated staffing type
   */
  async updateStaffingType(id: number | string, staffingType: StaffingType): Promise<StaffingType> {
    console.log(`StaffingTypeService: Updating staffing type with ID ${id}:`, staffingType);

    // Try multiple endpoints
    const endpoints = [
      `${this.baseUrl}/staffing-types/update/${id}`,
      `${this.baseUrl}/api/staffing-types/${id}`,
      `${this.baseUrl}/api/v1/staffing-types/${id}`
    ];

    for (const endpoint of endpoints) {
      try {
        console.log(`StaffingTypeService: Trying endpoint: ${endpoint}`);
        const response = await fetch(endpoint, {
          method: 'PUT',
          headers: this.getAuthHeaders(),
          body: JSON.stringify(staffingType)
        });

        if (!response.ok) {
          console.warn(`StaffingTypeService: Endpoint ${endpoint} returned status ${response.status}`);
          continue;
        }

        const data = await response.json();
        console.log(`StaffingTypeService: Successfully updated staffing type from ${endpoint}:`, data);
        return data;
      } catch (endpointError) {
        console.warn(`StaffingTypeService: Error updating from ${endpoint}:`, endpointError);
      }
    }

    throw new Error(`Failed to update staffing type with ID ${id}`);
  }

  /**
   * Delete a staffing type
   * @param id Staffing type ID
   * @returns Promise with void
   */
  async deleteStaffingType(id: number | string): Promise<void> {
    console.log(`StaffingTypeService: Deleting staffing type with ID ${id}`);

    // Try multiple endpoints
    const endpoints = [
      `${this.baseUrl}/staffing-types/deleteById/${id}`,
      `${this.baseUrl}/api/staffing-types/${id}`,
      `${this.baseUrl}/api/v1/staffing-types/${id}`
    ];

    for (const endpoint of endpoints) {
      try {
        console.log(`StaffingTypeService: Trying endpoint: ${endpoint}`);
        const response = await fetch(endpoint, {
          method: 'DELETE',
          headers: this.getAuthHeaders()
        });

        if (!response.ok) {
          console.warn(`StaffingTypeService: Endpoint ${endpoint} returned status ${response.status}`);
          continue;
        }

        console.log(`StaffingTypeService: Successfully deleted staffing type from ${endpoint}`);
        return;
      } catch (endpointError) {
        console.warn(`StaffingTypeService: Error deleting from ${endpoint}:`, endpointError);
      }
    }

    throw new Error(`Failed to delete staffing type with ID ${id}`);
  }
}

export const staffingTypeService = new StaffingTypeService();
