@echo off
echo Starting backend server and testing project CRUD operations...

cd backend

echo.
echo ===== STEP 1: Starting Backend Server =====
echo Starting Spring Boot application on port 8091...
start "Backend Server" cmd /k "mvn spring-boot:run"

echo Waiting for backend to start...
timeout /t 15 /nobreak

echo.
echo ===== STEP 2: Testing Backend Connection =====
powershell -Command "try { $response = Invoke-RestMethod -Uri 'http://************:8091/projects' -Method Get; Write-Host 'Backend is running. Found' $response.Count 'projects' } catch { Write-Host 'Backend connection failed:' $_.Exception.Message }"

echo.
echo ===== STEP 3: Testing Project Creation =====
echo Creating a test project...
powershell -Command "try { $body = @{ name='Test Project'; clientId=1; description='Test project for CRUD operations'; value=10000 } | ConvertTo-Json; $response = Invoke-RestMethod -Uri 'http://************:8091/projects' -Method Post -Body $body -ContentType 'application/json'; Write-Host 'Project created successfully with ID:' $response.id } catch { Write-Host 'Project creation failed:' $_.Exception.Message }"

echo.
echo ===== STEP 4: Testing Project Update =====
echo Updating the test project...
powershell -Command "try { $body = @{ name='Updated Test Project'; clientId=1; description='Updated test project'; value=15000 } | ConvertTo-Json; $response = Invoke-RestMethod -Uri 'http://************:8091/projects/1' -Method Put -Body $body -ContentType 'application/json'; Write-Host 'Project updated successfully' } catch { Write-Host 'Project update failed:' $_.Exception.Message }"

echo.
echo ===== STEP 5: Testing Project Deletion =====
echo Deleting the test project...
powershell -Command "try { Invoke-RestMethod -Uri 'http://************:8091/projects/1' -Method Delete; Write-Host 'Project deleted successfully' } catch { Write-Host 'Project deletion failed:' $_.Exception.Message }"

echo.
echo ===== CRUD Test Complete =====
echo.
echo The backend server is now running in a separate window.
echo You can now test the frontend at: http://************:3060/projects
echo.
echo Press any key to continue...
pause
