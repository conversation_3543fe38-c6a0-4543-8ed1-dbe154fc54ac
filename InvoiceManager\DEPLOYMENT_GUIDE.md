# 🚀 Deployment Guide - Server Migration

## Quick IP Change Commands

### Automatic Configuration (Recommended)
```bash
# Change to new IP
cd frontend
node setup-env.js --ip=NEW_IP_ADDRESS

# Examples:
node setup-env.js --ip=************    # Different machine same network
node setup-env.js --ip=**********      # Different network
node setup-env.js --ip=************    # Public IP/Cloud server
node setup-env.js --ip=myserver.com    # Domain name
```

### Manual Configuration Files

#### 1. Frontend Environment
**File:** `frontend/.env`
```bash
# Change this line:
VITE_API_URL=http://NEW_IP_ADDRESS:8091/api
```

#### 2. Backend Configuration  
**File:** `backend/src/main/resources/application.properties`
```properties
# Change this line:
onedrive.redirect.uri=http://NEW_IP_ADDRESS:8091/api/onedrive/callback
```

#### 3. Root Controller
**File:** `backend/src/main/java/com/redberyl/invoiceapp/controller/RootController.java`
```java
// Change this line:
return new RedirectView("http://NEW_IP_ADDRESS:3060");
```

## Server Migration Checklist

### Pre-Migration
- [ ] Note current IP: `ipconfig` or `ip addr`
- [ ] Backup current configuration
- [ ] Test application locally

### Migration Steps
1. **Copy application to new server**
2. **Get new server IP:**
   ```bash
   ipconfig          # Windows
   ip addr show      # Linux
   hostname -I       # Linux alternative
   ```

3. **Configure new IP:**
   ```bash
   cd frontend
   node setup-env.js --ip=NEW_SERVER_IP
   ```

4. **Update firewall (Windows):**
   ```cmd
   netsh advfirewall firewall add rule name="Invoice App Frontend" dir=in action=allow protocol=TCP localport=3060
   netsh advfirewall firewall add rule name="Invoice App Backend" dir=in action=allow protocol=TCP localport=8091
   ```

5. **Update firewall (Linux):**
   ```bash
   sudo ufw allow 3060/tcp
   sudo ufw allow 8091/tcp
   sudo ufw reload
   ```

6. **Start application:**
   ```bash
   # Windows
   .\start-network.bat
   
   # Linux/Manual
   cd backend && mvn spring-boot:run &
   cd frontend && npm run dev
   ```

### Post-Migration
- [ ] Test local access: `http://localhost:3060`
- [ ] Test network access: `http://NEW_IP:3060`
- [ ] Test from other devices
- [ ] Verify API calls work
- [ ] Test OneDrive integration (if used)

## Deployment Scenarios

### Scenario 1: Local Network Server
```bash
# Example: Home/Office server
# Old IP: ************
# New IP: *************

cd frontend
node setup-env.js --ip=*************
```

### Scenario 2: Cloud Server (AWS/Azure/GCP)
```bash
# Example: Cloud instance with public IP
# Public IP: ************

cd frontend
node setup-env.js --ip=************

# Note: You may need to configure cloud security groups/firewall
```

### Scenario 3: Docker Deployment
```bash
# If deploying in Docker, use host IP
# Get Docker host IP first

cd frontend
node setup-env.js --ip=DOCKER_HOST_IP
```

### Scenario 4: Domain Name
```bash
# If you have a domain pointing to your server
cd frontend
node setup-env.js --ip=myinvoiceapp.example.com
```

## Production Considerations

### Security
- Use HTTPS in production
- Configure proper firewall rules
- Consider VPN for remote access
- Use environment variables for sensitive data

### Performance
- Use production build for frontend: `npm run build`
- Configure reverse proxy (nginx/Apache)
- Set up SSL certificates
- Enable gzip compression

### Monitoring
- Set up application logs
- Monitor server resources
- Configure health checks
- Set up backup procedures

## Troubleshooting

### Common Issues

#### "Connection Refused"
- Check if services are running: `netstat -an | findstr :3060`
- Verify firewall rules
- Confirm IP address is correct

#### "API Calls Failing"
- Check frontend .env file
- Verify backend is accessible
- Test API endpoint: `curl http://NEW_IP:8091/api/health`

#### "OneDrive Integration Not Working"
- Update redirect URI in application.properties
- Update OneDrive app registration if using custom domain
- Check OAuth callback URL

### Quick Diagnostics
```bash
# Check if ports are open
netstat -an | findstr :3060
netstat -an | findstr :8091

# Test connectivity
ping NEW_IP_ADDRESS
telnet NEW_IP_ADDRESS 3060
telnet NEW_IP_ADDRESS 8091

# Check application logs
# Backend: Console output from mvn spring-boot:run
# Frontend: Browser developer console
```

## Rollback Plan

If deployment fails:
1. **Restore original IP configuration:**
   ```bash
   cd frontend
   node setup-env.js --ip=ORIGINAL_IP
   ```

2. **Restart services**
3. **Verify functionality**

## Environment Variables (Advanced)

For production deployments, consider using environment variables:

```bash
# Set environment variables
export INVOICE_APP_IP=************
export INVOICE_APP_FRONTEND_PORT=3060
export INVOICE_APP_BACKEND_PORT=8091

# Use in configuration
VITE_API_URL=http://${INVOICE_APP_IP}:${INVOICE_APP_BACKEND_PORT}/api
```

This allows for easier deployment across different environments without code changes.
