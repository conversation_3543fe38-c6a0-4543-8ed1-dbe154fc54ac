# 🔧 Network Access Troubleshooting Guide

## ❌ Problem: Cannot Access from Another Server/Device

### ✅ **FIXED: CORS Configuration Updated**
I've updated your `WebSecurityConfig.java` to allow access from all networks:
- ✅ Private networks (192.168.x.x, 10.x.x.x, 172.16-31.x.x)
- ✅ Public IP addresses
- ✅ Domain names
- ✅ HTTPS support

### 🔍 **Step-by-Step Troubleshooting**

#### 1. **Verify Server Configuration**
```bash
# Check if backend is listening on all interfaces
netstat -an | findstr :8091
# Should show: 0.0.0.0:8091 or *:8091

# Check if frontend is accessible
netstat -an | findstr :3060
# Should show: 0.0.0.0:3060 or *:3060
```

#### 2. **Test Network Connectivity**
```bash
# From the other server, test if you can reach the host
ping YOUR_SERVER_IP

# Test if ports are reachable
telnet YOUR_SERVER_IP 8091  # Backend
telnet YOUR_SERVER_IP 3060  # Frontend

# Alternative port test (Windows)
Test-NetConnection YOUR_SERVER_IP -Port 8091
Test-NetConnection YOUR_SERVER_IP -Port 3060
```

#### 3. **Check Firewall Settings**

**On the server hosting the application:**
```bash
# Windows - Add firewall rules
netsh advfirewall firewall add rule name="Invoice App Frontend" dir=in action=allow protocol=TCP localport=3060
netsh advfirewall firewall add rule name="Invoice App Backend" dir=in action=allow protocol=TCP localport=8091

# Windows - Check existing rules
netsh advfirewall firewall show rule name="Invoice App Frontend"
netsh advfirewall firewall show rule name="Invoice App Backend"

# Linux - Add firewall rules
sudo ufw allow 3060/tcp
sudo ufw allow 8091/tcp
sudo ufw reload

# Linux - Check status
sudo ufw status
```

#### 4. **Verify Application Configuration**

**Check frontend .env file:**
```bash
type InvoiceManager\frontend\.env
# Should show: VITE_API_URL=http://YOUR_SERVER_IP:8091/api
```

**Check if services are running:**
```bash
# Backend should show Spring Boot startup logs
# Frontend should show Vite dev server running on 0.0.0.0:3060
```

#### 5. **Test API Endpoints**
```bash
# Test backend API directly
curl http://YOUR_SERVER_IP:8091/api/auth/test
# or
curl http://YOUR_SERVER_IP:8091/api/examples

# Test from browser on other device
http://YOUR_SERVER_IP:8091/api/examples
```

### 🌐 **Network Configuration Examples**

#### Same Network (192.168.x.x)
```bash
# Server IP: ************
# Other device: ************
# Access: http://************:3060
```

#### Different Private Network (10.x.x.x)
```bash
# Server IP: **********
# Other device: **********
# Access: http://**********:3060
```

#### Corporate Network (172.16-31.x.x)
```bash
# Server IP: ************
# Other device: ************
# Access: http://************:3060
```

#### Cloud/Public IP
```bash
# Server IP: ************
# Access from anywhere: http://************:3060
```

### 🚨 **Common Issues & Solutions**

#### Issue 1: "Connection Refused"
**Causes:**
- Firewall blocking ports
- Services not running
- Wrong IP address

**Solutions:**
```bash
# Check if services are running
netstat -an | findstr :3060
netstat -an | findstr :8091

# Restart services
cd InvoiceManager
.\start-network.bat

# Check firewall
netsh advfirewall firewall show rule name=all | findstr "3060\|8091"
```

#### Issue 2: "CORS Error" in Browser
**Cause:** CORS configuration (FIXED in WebSecurityConfig.java)

**Verify fix:**
- Check browser developer console
- Should not see CORS errors anymore

#### Issue 3: "API Calls Failing"
**Causes:**
- Wrong API URL in frontend
- Backend not accessible

**Solutions:**
```bash
# Check frontend configuration
type InvoiceManager\frontend\.env

# Test backend directly
curl http://YOUR_SERVER_IP:8091/api/examples

# Update configuration if needed
cd InvoiceManager\frontend
node setup-env.js --ip=YOUR_SERVER_IP
```

#### Issue 4: "Timeout" Errors
**Causes:**
- Network routing issues
- Firewall dropping packets
- Services overloaded

**Solutions:**
```bash
# Check network path
tracert YOUR_SERVER_IP

# Check server resources
# Task Manager > Performance (Windows)
# htop (Linux)

# Restart services
cd InvoiceManager
.\start-network.bat
```

### 🔒 **Security Considerations**

#### For Production Deployment:
1. **Use HTTPS instead of HTTP**
2. **Restrict CORS to specific domains**
3. **Use VPN for remote access**
4. **Configure proper authentication**

#### Update CORS for Production:
```java
// In WebSecurityConfig.java, replace with specific domains:
configuration.addAllowedOrigin("https://yourdomain.com");
configuration.addAllowedOrigin("https://app.yourdomain.com");
```

### 📋 **Quick Diagnostic Commands**

```bash
# Get your server IP
ipconfig | findstr IPv4

# Check if ports are open
netstat -an | findstr :3060
netstat -an | findstr :8091

# Test from another machine
ping YOUR_SERVER_IP
telnet YOUR_SERVER_IP 3060
telnet YOUR_SERVER_IP 8091

# Check firewall rules
netsh advfirewall firewall show rule name=all | findstr "3060\|8091"

# Verify configuration
type InvoiceManager\frontend\.env
```

### 🆘 **If Still Not Working**

1. **Restart everything:**
   ```bash
   cd InvoiceManager
   .\start-network.bat
   ```

2. **Check Windows Event Logs**
3. **Temporarily disable Windows Firewall for testing**
4. **Try accessing from the same machine first**
5. **Check antivirus software blocking connections**

### 📞 **Contact Information**
If you're still having issues, provide:
- Server IP address
- Client IP address
- Error messages from browser console
- Output from diagnostic commands above
