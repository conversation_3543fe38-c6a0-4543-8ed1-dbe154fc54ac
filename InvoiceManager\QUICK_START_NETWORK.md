# 🚀 Quick Start - Network Access Setup

Your Invoice Manager application is now configured for network access! Here's everything you need to know:

## ✅ Current Configuration

**Your IP Address:** `************`
**Frontend Port:** `3060`
**Backend Port:** `8091`

### Access URLs
- **Local (on your machine):** http://localhost:3060
- **Network (from other devices):** http://************:3060

## 🎯 How to Start the Application

### Option 1: Quick Start (Recommended)
```bash
# Run this single command to start everything
.\start-network.bat
```

### Option 2: Manual Start
```bash
# Terminal 1: Start Backend
cd backend
mvn spring-boot:run

# Terminal 2: Start Frontend (in a new terminal)
cd frontend
npm run dev
```

## 📱 Accessing from Other Devices

1. **Make sure both devices are on the same WiFi network**
2. **Open a web browser on the other device**
3. **Navigate to:** http://************:3060

### Supported Devices
- ✅ Other Windows computers
- ✅ Mac computers
- ✅ Android phones/tablets
- ✅ iPhones/iPads
- ✅ Any device with a web browser

## 🔥 Firewall Setup (Important!)

### Automatic Setup (Run as Administrator)
```cmd
netsh advfirewall firewall add rule name="Invoice App Frontend" dir=in action=allow protocol=TCP localport=3060
netsh advfirewall firewall add rule name="Invoice App Backend" dir=in action=allow protocol=TCP localport=8091
```

### Manual Setup
1. Open Windows Defender Firewall
2. Click "Advanced settings"
3. Click "Inbound Rules" → "New Rule"
4. Select "Port" → "TCP" → "Specific local ports"
5. Enter `3060` for frontend, then repeat for `8091` for backend

## 🔧 Configuration Scripts

### Reconfigure Network Settings
```bash
# Auto-detect IP and configure
scripts\configure-network.bat

# Use specific IP
cd frontend
node setup-env.js --ip=YOUR_NEW_IP

# Use localhost only
node setup-env.js --localhost
```

### If Your IP Changes
```bash
# Get your new IP
ipconfig

# Reconfigure with new IP (replace with your actual IP)
cd frontend
node setup-env.js --ip=192.168.1.XXX
```

## 🛠️ Troubleshooting

### Problem: Can't access from other devices
**Solution:** Check Windows Firewall settings
```bash
# Test if ports are open
netstat -an | findstr :3060
netstat -an | findstr :8091
```

### Problem: "Connection refused" error
**Solutions:**
1. Make sure both frontend and backend are running
2. Check firewall rules are configured
3. Verify both devices are on same network

### Problem: API calls not working
**Solution:** Check environment configuration
```bash
# Verify frontend .env file
type frontend\.env
# Should show: VITE_API_URL=http://************:8091/api
```

### Problem: Services won't start
**Check prerequisites:**
```bash
java -version    # Should show Java 17+
node --version   # Should show Node.js
mvn --version    # Should show Maven
```

## 📋 Network Information

### Current Network Setup
- **Network:** ***********/24
- **Gateway:** ***********
- **Your IP:** ************
- **Subnet Mask:** *************

### Ports Used
- **3060** - Frontend (Vite development server)
- **8091** - Backend (Spring Boot application)
- **5432** - Database (PostgreSQL, local only)

## 🔒 Security Notes

- Application is configured for local network access only
- No internet exposure by default
- Only devices on your WiFi network can access the application
- Consider using VPN for remote access instead of port forwarding

## 📞 Quick Commands Reference

```bash
# Start everything
.\start-network.bat

# Configure network
scripts\configure-network.bat

# Check IP address
ipconfig

# Test connectivity
ping ************

# Check if services are running
netstat -an | findstr :3060
netstat -an | findstr :8091
```

## 🎉 You're All Set!

Your Invoice Manager is now ready for network access. Other devices on your network can access it at:

**http://************:3060**

Enjoy using your Invoice Manager from any device! 🚀
