#!/usr/bin/env node

/**
 * Setup script to configure the environment file with the correct IP address
 * This script can be run on any system to automatically configure the backend URL
 */

import fs from 'fs';
import path from 'path';
import os from 'os';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

function getLocalIPAddress() {
  const interfaces = os.networkInterfaces();

  for (const name of Object.keys(interfaces)) {
    for (const networkInterface of interfaces[name]) {
      // Skip over non-IPv4 and internal (i.e. 127.0.0.1) addresses
      if (networkInterface.family === 'IPv4' && !networkInterface.internal) {
        return networkInterface.address;
      }
    }
  }

  return 'localhost';
}

function createEnvFile(ipAddress, useLocalhost = false) {
  const envPath = path.join(__dirname, '.env');
  const apiUrl = useLocalhost ? 'http://localhost:8091/api' : `http://${ipAddress}:8091/api`;
  const baseUrl = useLocalhost ? 'http://localhost:8091' : `http://${ipAddress}:8091`;

  const envContent = `# Auto-generated environment file
# Backend API URL
VITE_API_URL=${apiUrl}

# Base URL without /api suffix for direct calls
VITE_API_BASE_URL=${baseUrl}

# Generated on: ${new Date().toISOString()}
# IP Address: ${ipAddress}
# Mode: ${useLocalhost ? 'localhost' : 'network'}
`;

  fs.writeFileSync(envPath, envContent);
  console.log(`✅ Environment file created: ${envPath}`);
  console.log(`🌐 API URL: ${apiUrl}`);
  console.log(`🌐 Base URL: ${baseUrl}`);
}

function main() {
  const args = process.argv.slice(2);
  const useLocalhost = args.includes('--localhost') || args.includes('-l');
  const customIP = args.find(arg => arg.startsWith('--ip='))?.split('=')[1];
  
  if (customIP) {
    console.log(`🔧 Using custom IP: ${customIP}`);
    createEnvFile(customIP);
  } else if (useLocalhost) {
    console.log('🏠 Using localhost mode');
    createEnvFile('localhost', true);
  } else {
    const localIP = getLocalIPAddress();
    console.log(`🔍 Detected local IP: ${localIP}`);
    createEnvFile(localIP);
  }
  
  console.log('\n📝 Usage:');
  console.log('  node setup-env.js                    # Auto-detect IP');
  console.log('  node setup-env.js --localhost        # Use localhost');
  console.log('  node setup-env.js --ip=************* # Use custom IP');
}

// Run main function
main();

export { getLocalIPAddress, createEnvFile };
