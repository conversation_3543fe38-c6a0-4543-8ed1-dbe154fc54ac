package com.redberyl.invoiceapp.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.web.servlet.config.annotation.ContentNegotiationConfigurer;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.List;

@Configuration
public class WebConfig implements WebMvcConfigurer {

        @Override
        public void configureContentNegotiation(ContentNegotiationConfigurer configurer) {
                configurer
                                .favorParameter(false)
                                .ignoreAcceptHeader(false)
                                .useRegisteredExtensionsOnly(false)
                                .defaultContentType(MediaType.APPLICATION_JSON)
                                .mediaType("json", MediaType.APPLICATION_JSON);
        }

        @Autowired
        private ObjectMapper objectMapper;

        @Override
        public void configureMessageConverters(List<HttpMessageConverter<?>> converters) {
                // Add UTF-8 string converter
                StringHttpMessageConverter stringConverter = new StringHttpMessageConverter(StandardCharsets.UTF_8);
                stringConverter.setSupportedMediaTypes(Arrays.asList(
                                MediaType.TEXT_PLAIN,
                                MediaType.TEXT_HTML,
                                MediaType.APPLICATION_JSON));
                converters.add(stringConverter);

                // Add Jackson JSON converter
                MappingJackson2HttpMessageConverter jsonConverter = new MappingJackson2HttpMessageConverter();
                jsonConverter.setSupportedMediaTypes(Arrays.asList(
                                MediaType.APPLICATION_JSON,
                                MediaType.valueOf("application/json;charset=UTF-8"),
                                MediaType.valueOf("application/*+json;charset=UTF-8"),
                                MediaType.valueOf("application/*+json")));
                jsonConverter.setObjectMapper(objectMapper);
                converters.add(jsonConverter);
        }

        @Override
        public void addCorsMappings(CorsRegistry registry) {
                registry.addMapping("/**")
                                .allowedOriginPatterns(
                                    "http://localhost:*",
                                    "http://127.0.0.1:*",
                                    "http://192.168.*.*:*"
                                ) // Use specific patterns instead of "*" when allowCredentials is true
                                .allowedOrigins(
                                    "http://localhost:3060",
                                    "http://127.0.0.1:3060",
                                    "http://************:3060"
                                ) // Explicitly allow frontend dev server port
                                .allowedMethods("GET", "POST", "PUT", "PATCH", "DELETE", "OPTIONS", "HEAD")
                                .allowedHeaders("*") // Allow all headers
                                .exposedHeaders("Access-Control-Allow-Origin", "Access-Control-Allow-Methods",
                                                "Access-Control-Allow-Headers", "Access-Control-Max-Age",
                                                "Authorization", "X-Auth-Token")
                                .allowCredentials(true) // Allow credentials
                                .maxAge(3600);
        }

        @Override
        public void addResourceHandlers(ResourceHandlerRegistry registry) {
                // Only serve backend static resources, don't interfere with frontend routing
                registry.addResourceHandler("/static/**")
                                .addResourceLocations("classpath:/static/");

                // Serve images for backend use (like invoice templates)
                registry.addResourceHandler("/images/**")
                                .addResourceLocations("classpath:/static/images/");

                // Serve swagger UI resources
                registry.addResourceHandler("/swagger-ui/**")
                                .addResourceLocations("classpath:/static/swagger-ui/");
        }

}
