@echo off
echo ========================================
echo Fixing Remaining localhost URLs
echo ========================================
echo.

echo Updating remaining service files...

cd frontend\src\services

echo Fixing dashboard service...
powershell -Command "(Get-Content dashboardService.ts) -replace 'http://localhost:8091', '${import.meta.env.VITE_API_BASE_URL || \"http://************:8091\"}' | Set-Content dashboardService.ts"

echo Fixing GST challan service...
powershell -Command "(Get-Content gstChallanService.ts) -replace 'http://localhost:8091', '${import.meta.env.VITE_API_BASE_URL || \"http://************:8091\"}' | Set-Content gstChallanService.ts"

echo Fixing invoice service...
powershell -Command "(Get-Content invoiceService.ts) -replace 'http://localhost:8091', '${import.meta.env.VITE_API_BASE_URL || \"http://************:8091\"}' | Set-Content invoiceService.ts"

echo Fixing payment service...
powershell -Command "(Get-Content paymentService.ts) -replace 'http://localhost:8091', '${import.meta.env.VITE_API_BASE_URL || \"http://************:8091\"}' | Set-Content paymentService.ts"

echo Fixing redberyl account service...
powershell -Command "(Get-Content redberylAccountService.ts) -replace 'http://localhost:8091', '${import.meta.env.VITE_API_BASE_URL || \"http://************:8091\"}' | Set-Content redberylAccountService.ts"

echo.
echo ========================================
echo All localhost URLs have been updated!
echo ========================================
echo.
echo Please restart your frontend:
echo   cd ..\..\..
echo   npm run dev
echo.
pause
